import { z } from 'zod';
import {
  ProductSchema,
  HackProductSchema,
  UCProductSchema,
  AccountProductSchema,
  UserSchema,
  OrderSchema,
  ProductType,
  UserRole,
  OrderStatus,
  PaymentStatus
} from './types';

// Form validation schemas
export const ProductFormSchema = z.object({
  type: z.nativeEnum(ProductType),
  name: z.object({
    en: z.string().min(1, 'English name is required').max(100, 'Name too long'),
    ar: z.string().min(1, 'Arabic name is required').max(100, 'Name too long')
  }),
  description: z.object({
    en: z.string().min(10, 'Description must be at least 10 characters').max(1000, 'Description too long'),
    ar: z.string().min(10, 'Description must be at least 10 characters').max(1000, 'Description too long')
  }),
  price: z.object({
    egp: z.number().positive('EGP price must be positive'),
    usd: z.number().positive('USD price must be positive')
  }),
  category: z.object({
    en: z.string().optional(),
    ar: z.string().optional()
  }).optional(),
  tags: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  stockCount: z.number().int().min(0, 'Stock count cannot be negative').default(0),
  lowStockThreshold: z.number().int().min(0, 'Threshold cannot be negative').default(5),
  images: z.array(z.string().url('Invalid image URL')).default([])
});

export const HackProductFormSchema = ProductFormSchema.extend({
  type: z.literal(ProductType.HACK),
  specs: z.record(z.object({
    en: z.string(),
    ar: z.string()
  })).optional(),
  features: z.object({
    en: z.array(z.string()),
    ar: z.array(z.string())
  }).optional(),
  compatibility: z.object({
    en: z.string(),
    ar: z.string()
  }).optional(),
  detectionStatus: z.object({
    en: z.string(),
    ar: z.string()
  }).optional(),
  version: z.string().optional(),
  fileSize: z.string().optional()
});

export const UCProductFormSchema = ProductFormSchema.extend({
  type: z.literal(ProductType.UC),
  quantity: z.number().int().positive('UC quantity must be positive'),
  bonusPercentage: z.number().min(0).max(100, 'Bonus percentage must be between 0-100').default(0),
  deliveryTime: z.object({
    en: z.string(),
    ar: z.string()
  }).optional(),
  features: z.object({
    en: z.array(z.string()),
    ar: z.array(z.string())
  }).optional()
});

export const AccountProductFormSchema = ProductFormSchema.extend({
  type: z.literal(ProductType.ACCOUNT),
  specs: z.record(z.object({
    en: z.string(),
    ar: z.string()
  })).optional(),
  features: z.object({
    en: z.array(z.string()),
    ar: z.array(z.string())
  }).optional(),
  gameType: z.object({
    en: z.string(),
    ar: z.string()
  }).optional(),
  accountLevel: z.number().int().min(1).optional(),
  rank: z.object({
    en: z.string(),
    ar: z.string()
  }).optional(),
  skins: z.array(z.object({
    en: z.string(),
    ar: z.string()
  })).default([]),
  achievements: z.array(z.object({
    en: z.string(),
    ar: z.string()
  })).default([])
});

export const UserFormSchema = z.object({
  email: z.string().email('Invalid email address'),
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name too long'),
  firstName: z.string().max(50, 'First name too long').optional(),
  lastName: z.string().max(50, 'Last name too long').optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number').optional(),
  role: z.nativeEnum(UserRole).default(UserRole.CUSTOMER),
  isActive: z.boolean().default(true),
  country: z.string().max(50, 'Country name too long').optional(),
  city: z.string().max(50, 'City name too long').optional(),
  dateOfBirth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)').optional(),
  bio: z.string().max(500, 'Bio too long').optional()
});

export const OrderFormSchema = z.object({
  customerId: z.string().min(1, 'Customer ID is required'),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product ID is required'),
    quantity: z.number().int().positive('Quantity must be positive'),
    unitPrice: z.object({
      egp: z.number().positive(),
      usd: z.number().positive()
    })
  })).min(1, 'At least one item is required'),
  status: z.nativeEnum(OrderStatus).default(OrderStatus.PENDING),
  paymentStatus: z.nativeEnum(PaymentStatus).default(PaymentStatus.PENDING),
  paymentMethod: z.string().optional(),
  notes: z.string().max(1000, 'Notes too long').optional(),
  deliveryInstructions: z.string().max(500, 'Delivery instructions too long').optional()
});

// Search and filter validation schemas
export const ProductFiltersSchema = z.object({
  type: z.nativeEnum(ProductType).optional(),
  category: z.string().optional(),
  priceRange: z.object({
    min: z.number().min(0),
    max: z.number().min(0),
    currency: z.enum(['egp', 'usd'])
  }).optional(),
  isActive: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  inStock: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  search: z.string().max(100, 'Search term too long').optional()
});

export const OrderFiltersSchema = z.object({
  status: z.nativeEnum(OrderStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  customerId: z.string().optional(),
  dateRange: z.object({
    start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid start date format'),
    end: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid end date format')
  }).optional(),
  search: z.string().max(100, 'Search term too long').optional()
});

export const UserFiltersSchema = z.object({
  role: z.nativeEnum(UserRole).optional(),
  isActive: z.boolean().optional(),
  country: z.string().optional(),
  registrationDateRange: z.object({
    start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid start date format'),
    end: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid end date format')
  }).optional(),
  search: z.string().max(100, 'Search term too long').optional()
});

// Pagination schema
export const PaginationSchema = z.object({
  page: z.number().int().min(1, 'Page must be at least 1').default(1),
  limit: z.number().int().min(1, 'Limit must be at least 1').max(100, 'Limit cannot exceed 100').default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Bulk operation schemas
export const BulkProductUpdateSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one product ID is required'),
  updates: z.object({
    isActive: z.boolean().optional(),
    isFeatured: z.boolean().optional(),
    category: z.object({
      en: z.string(),
      ar: z.string()
    }).optional(),
    tags: z.array(z.string()).optional(),
    price: z.object({
      egp: z.number().positive(),
      usd: z.number().positive()
    }).optional()
  })
});

export const BulkDeleteSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one ID is required')
});

// File upload schema
export const FileUploadSchema = z.object({
  file: z.instanceof(File, 'Invalid file'),
  maxSize: z.number().default(5 * 1024 * 1024), // 5MB default
  allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/webp'])
});

// Validation helper functions
export const validateProductForm = (data: unknown, productType: ProductType) => {
  switch (productType) {
    case ProductType.HACK:
      return HackProductFormSchema.parse(data);
    case ProductType.UC:
      return UCProductFormSchema.parse(data);
    case ProductType.ACCOUNT:
      return AccountProductFormSchema.parse(data);
    default:
      return ProductFormSchema.parse(data);
  }
};

export const validateFile = (file: File, maxSize: number = 5 * 1024 * 1024, allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp']) => {
  if (file.size > maxSize) {
    throw new Error(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
  }
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }
  
  return true;
};

export const sanitizeSearchTerm = (term: string): string => {
  return term.trim().toLowerCase().replace(/[^\w\s\u0600-\u06FF]/g, '');
};

export const validatePriceRange = (min: number, max: number): boolean => {
  return min >= 0 && max >= min;
};

export const validateDateRange = (start: string, end: string): boolean => {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return startDate <= endDate && startDate <= new Date();
};

// Type exports for form validation
export type ProductFormData = z.infer<typeof ProductFormSchema>;
export type HackProductFormData = z.infer<typeof HackProductFormSchema>;
export type UCProductFormData = z.infer<typeof UCProductFormSchema>;
export type AccountProductFormData = z.infer<typeof AccountProductFormSchema>;
export type UserFormData = z.infer<typeof UserFormSchema>;
export type OrderFormData = z.infer<typeof OrderFormSchema>;
export type ProductFiltersData = z.infer<typeof ProductFiltersSchema>;
export type OrderFiltersData = z.infer<typeof OrderFiltersSchema>;
export type UserFiltersData = z.infer<typeof UserFiltersSchema>;
export type PaginationData = z.infer<typeof PaginationSchema>;
export type BulkProductUpdateData = z.infer<typeof BulkProductUpdateSchema>;
export type BulkDeleteData = z.infer<typeof BulkDeleteSchema>;
