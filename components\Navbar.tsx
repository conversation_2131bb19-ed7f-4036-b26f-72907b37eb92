"use client"

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/contexts/AuthContext";
import { ShoppingCart, Menu, X, User, LogOut, Settings } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { user, signOut } = useAuth();
  const { t } = useLanguage();
  const pathname = usePathname();
  const { toast } = useToast();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  // Navigation links
  const navLinks = [
    { name: t('home'), path: "/" },
    { name: t('hacks'), path: "/hacks" },
    { name: t('uc'), path: "/uc" },
    { name: t('accounts'), path: "/accounts" },
  ];

  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Success",
        description: "Logged out successfully!",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Parse the site name with styling
  const parseSiteName = () => {
    const parts = "RNG-STORE".split('-');
    if (parts.length > 1) {
      return (
        <>
          {parts[0]}<span className="text-yellow-500">-{parts.slice(1).join('-')}</span>
        </>
      );
    }
    return "RNG-STORE";
  };

  return (
    <header
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${
        isScrolled
          ? "bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-800 py-2"
          : "bg-transparent py-4"
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        {/* Logo */}
        <Link href="/" className="flex items-center">
          <h1 className="text-2xl font-bold text-white">{parseSiteName()}</h1>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-1 space-x-reverse">
          {navLinks.map((link) => (
            <Link
              key={link.path}
              href={link.path}
              className={`px-4 py-2 mx-1 rounded-lg text-sm font-medium transition-all duration-300 ${
                pathname === link.path
                  ? "text-yellow-500 bg-gray-800/50"
                  : "text-white hover:text-yellow-500 hover:bg-gray-800/30"
              }`}
            >
              {link.name}
            </Link>
          ))}
        </nav>

        {/* User Actions - Desktop */}
        <div className="hidden md:flex items-center gap-1">
          {/* Language Switcher */}
          <LanguageSwitcher />
          
          {/* Cart Button */}
          <Link href="/cart" className="relative ml-2">
            <Button variant="ghost" size="icon" className="text-white hover:text-yellow-500 hover:bg-gray-800/30">
              <ShoppingCart size={20} />
              {/* You can add cart count here if needed */}
            </Button>
          </Link>

          {/* User Menu */}
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white hover:text-yellow-500 hover:bg-gray-800/30">
                  {user.photoURL ? (
                    <Avatar className="h-9 w-9 border-2 border-yellow-500">
                      <AvatarImage
                        src={user.photoURL}
                        alt={user.displayName || "User"}
                        className="object-cover"
                      />
                      <AvatarFallback className="bg-gray-800 text-white">
                        {user.displayName ? user.displayName.charAt(0).toUpperCase() : "U"}
                      </AvatarFallback>
                    </Avatar>
                  ) : (
                    <User size={20} />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="cursor-pointer">
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/admin" className="cursor-pointer">
                    <Settings className="mr-2 h-4 w-4" />
                    Admin
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-500 focus:text-red-500"
                  onClick={handleLogout}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button asChild variant="default" className="bg-yellow-500 text-black hover:bg-yellow-600 font-semibold">
              <Link href="/auth">Login</Link>
            </Button>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden flex items-center">
          <LanguageSwitcher />
          
          <Link href="/cart" className="relative ml-4">
            <Button variant="ghost" size="icon" className="text-white hover:text-yellow-500 hover:bg-gray-800/30">
              <ShoppingCart size={20} />
            </Button>
          </Link>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="text-white hover:text-yellow-500 hover:bg-gray-800/30 ml-2"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-gray-900/95 backdrop-blur-md border-b border-gray-800"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-2">
                {navLinks.map((link) => (
                  <Link
                    key={link.path}
                    href={link.path}
                    className={`px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 ${
                      pathname === link.path
                        ? "bg-gray-800 text-yellow-500"
                        : "text-white hover:bg-gray-800/50 hover:text-yellow-500"
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {link.name}
                  </Link>
                ))}

                {user ? (
                  <>
                    <Link
                      href="/profile"
                      className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 text-white hover:bg-gray-800/50 hover:text-yellow-500"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <User className="inline mr-2 h-5 w-5" />
                      Profile
                    </Link>
                    <Link
                      href="/admin"
                      className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 text-white hover:bg-accent/50 hover:text-pubg-orange"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Settings className="inline mr-2 h-5 w-5" />
                      Admin
                    </Link>
                    <button
                      className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 text-red-500 hover:bg-red-500/10 flex items-center"
                      onClick={() => {
                        handleLogout();
                        setIsMenuOpen(false);
                      }}
                    >
                      <LogOut className="mr-2 h-5 w-5" />
                      Logout
                    </button>
                  </>
                ) : (
                  <Link
                    href="/auth"
                    className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Login
                  </Link>
                )}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Navbar;
