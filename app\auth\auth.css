/* Optimized Auth Page Styles */

/* Smooth animations using CSS instead of heavy JS libraries */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-top {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom-4 {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation utility classes */
.animate-in {
  animation-fill-mode: both;
}

.fade-in {
  animation-name: fade-in;
}

.slide-in-from-top-4 {
  animation-name: slide-in-from-top;
}

.slide-in-from-top-2 {
  animation-name: slide-in-from-top-2;
}

.slide-in-from-bottom-4 {
  animation-name: slide-in-from-bottom-4;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.duration-700 {
  animation-duration: 700ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

/* Performance optimizations */
.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-200 {
  transition-duration: 200ms;
}

/* Hover effects */
.hover\:scale-\[1\.02\]:hover {
  transform: scale(1.02);
}

.active\:scale-\[0\.98\]:active {
  transform: scale(0.98);
}

/* Focus states for accessibility */
.focus\:border-pubg-orange:focus {
  border-color: #f2a900;
}

.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px rgba(242, 169, 0, 0.2);
}

.focus\:ring-pubg-orange\/20:focus {
  box-shadow: 0 0 0 2px rgba(242, 169, 0, 0.2);
}

/* Group hover effects */
.group:focus-within .group-focus-within\:text-pubg-orange {
  color: #f2a900;
}

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(242, 169, 0, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(242, 169, 0, 0.7);
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-in,
  .transition-all,
  .hover\:scale-\[1\.02\]:hover,
  .active\:scale-\[0\.98\]:active {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-slate-800\/50 {
    background-color: rgb(30 41 59 / 0.9);
  }

  .border-slate-700 {
    border-color: rgb(51 65 85);
  }

  .text-slate-400 {
    color: rgb(148 163 184);
  }
}

/* RTL Support - Scoped to auth page only */
.auth-page-rtl {
  direction: rtl;
  text-align: right;
}

.auth-page-rtl * {
  direction: inherit;
}

/* Auth page specific RTL styles */
.auth-page-rtl .flex {
  flex-direction: row-reverse;
}

.auth-page-rtl .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

.auth-page-rtl .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

/* Force RTL for auth form elements only */
.auth-page-rtl form,
.auth-page-rtl .form-item,
.auth-page-rtl .form-control {
  direction: rtl;
  text-align: right;
}

/* RTL Input styling - Auth page only */
.auth-page-rtl input[type="email"],
.auth-page-rtl input[type="password"],
.auth-page-rtl input[type="text"] {
  text-align: right !important;
  direction: rtl !important;
  padding-right: 2.5rem !important;
  padding-left: 3rem !important;
}

/* Override Tailwind classes for RTL - Auth page only */
.auth-page-rtl .pr-10 {
  padding-right: 2.5rem !important;
  padding-left: 0.75rem !important;
}

.auth-page-rtl .pl-10 {
  padding-left: 0.75rem !important;
  padding-right: 2.5rem !important;
}

.auth-page-rtl .pl-12 {
  padding-left: 0.75rem !important;
  padding-right: 3rem !important;
}

.auth-page-rtl .pr-12 {
  padding-right: 3rem !important;
  padding-left: 0.75rem !important;
}

/* RTL text alignment overrides - Auth page only */
.auth-page-rtl .text-right {
  text-align: right !important;
}

.auth-page-rtl input::placeholder {
  text-align: right !important;
  direction: rtl !important;
}

/* RTL Icon positioning - Auth page only */
.auth-page-rtl .right-3 {
  right: 0.75rem !important;
  left: auto !important;
}

.auth-page-rtl .left-3 {
  left: 0.75rem !important;
  right: auto !important;
}

/* Additional RTL positioning classes - Auth page only */
.auth-page-rtl .absolute.right-3 {
  right: 0.75rem !important;
  left: auto !important;
}

.auth-page-rtl .absolute.left-3 {
  left: 0.75rem !important;
  right: auto !important;
}

/* RTL Margin adjustments - Auth page only */
.auth-page-rtl .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.auth-page-rtl .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

.auth-page-rtl .ml-1 {
  margin-left: 0;
  margin-right: 0.25rem;
}

.auth-page-rtl .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.auth-page-rtl .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

/* RTL Animation adjustments - Auth page only */
.auth-page-rtl .slide-in-from-top-4 {
  animation-name: slide-in-from-top-rtl;
}

.auth-page-rtl .slide-in-from-bottom-4 {
  animation-name: slide-in-from-bottom-rtl;
}

@keyframes slide-in-from-top-rtl {
  from {
    opacity: 0;
    transform: translateY(-1rem) translateX(0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateX(0);
  }
}

@keyframes slide-in-from-bottom-rtl {
  from {
    opacity: 0;
    transform: translateY(1rem) translateX(0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateX(0);
  }
}

/* Arabic font optimization - Auth page only */
.auth-page-rtl * {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-feature-settings: "liga" 1, "kern" 1;
}

/* Better Arabic text rendering - Auth page only */
.auth-page-rtl h1,
.auth-page-rtl h2,
.auth-page-rtl h3,
.auth-page-rtl h4,
.auth-page-rtl h5,
.auth-page-rtl h6 {
  font-weight: 600;
  letter-spacing: 0.025em;
}

.auth-page-rtl p,
.auth-page-rtl span,
.auth-page-rtl label {
  line-height: 1.6;
}

/* RTL Button adjustments - Auth page only */
.auth-page-rtl button {
  text-align: center;
}

.auth-page-rtl .text-right {
  text-align: right;
}

/* Additional RTL fixes - Auth page only */
.auth-page-rtl .w-full {
  direction: rtl;
}

.auth-page-rtl .max-w-md {
  direction: rtl;
}

.auth-page-rtl .space-y-4 > * + *,
.auth-page-rtl .space-y-5 > * + *,
.auth-page-rtl .space-y-6 > * + * {
  direction: rtl;
}

/* Form specific RTL styling - Auth page only */
.auth-page-rtl form {
  direction: rtl !important;
}

.auth-page-rtl .form-item,
.auth-page-rtl [data-slot="form-item"] {
  direction: rtl !important;
  text-align: right !important;
}

.auth-page-rtl label {
  text-align: right !important;
  direction: rtl !important;
}

/* Button RTL styling - Auth page only */
.auth-page-rtl button {
  direction: rtl !important;
}

.auth-page-rtl .flex.items-center {
  flex-direction: row-reverse !important;
}

/* Card RTL styling - Auth page only */
.auth-page-rtl .bg-slate-800\/50 {
  direction: rtl;
}

/* Specific input field fixes - Auth page only */
.auth-page-rtl input {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext;
}

.auth-page-rtl input::placeholder {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext;
}
