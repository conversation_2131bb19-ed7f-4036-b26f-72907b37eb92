/* Optimized Auth Page Styles */

/* Smooth animations using CSS instead of heavy JS libraries */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-top {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom-4 {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation utility classes */
.animate-in {
  animation-fill-mode: both;
}

.fade-in {
  animation-name: fade-in;
}

.slide-in-from-top-4 {
  animation-name: slide-in-from-top;
}

.slide-in-from-top-2 {
  animation-name: slide-in-from-top-2;
}

.slide-in-from-bottom-4 {
  animation-name: slide-in-from-bottom-4;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.duration-700 {
  animation-duration: 700ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

/* Performance optimizations */
.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-200 {
  transition-duration: 200ms;
}

/* Hover effects */
.hover\:scale-\[1\.02\]:hover {
  transform: scale(1.02);
}

.active\:scale-\[0\.98\]:active {
  transform: scale(0.98);
}

/* Focus states for accessibility */
.focus\:border-pubg-orange:focus {
  border-color: #f2a900;
}

.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px rgba(242, 169, 0, 0.2);
}

.focus\:ring-pubg-orange\/20:focus {
  box-shadow: 0 0 0 2px rgba(242, 169, 0, 0.2);
}

/* Group hover effects */
.group:focus-within .group-focus-within\:text-pubg-orange {
  color: #f2a900;
}

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(242, 169, 0, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(242, 169, 0, 0.7);
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-in,
  .transition-all,
  .hover\:scale-\[1\.02\]:hover,
  .active\:scale-\[0\.98\]:active {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-slate-800\/50 {
    background-color: rgb(30 41 59 / 0.9);
  }
  
  .border-slate-700 {
    border-color: rgb(51 65 85);
  }
  
  .text-slate-400 {
    color: rgb(148 163 184);
  }
}
