import type { Metadata } from "next"
import ProductCard from "@/components/ProductCard"

export const metadata: Metadata = {
  title: "PUBG Hacks - RNG VIP",
  description: "Premium PUBG hacks and gaming tools. Enhance your gaming experience safely.",
}

// Mock data for hacks
const hacks = [
  {
    id: "1",
    name: { en: "ESP Hack Pro", ar: "هاك ESP برو" },
    description: { en: "Advanced ESP with enemy detection through walls", ar: "ESP متقدم مع كشف الأعداء عبر الجدران" },
    price: { egp: 450, usd: 15 },
    image: "/placeholder.svg?height=200&width=300",
    category: { en: "Vision", ar: "الرؤية" },
    type: "hack" as const,
  },
  {
    id: "2",
    name: { en: "Aimbot Elite", ar: "ايمبوت إليت" },
    description: {
      en: "Professional aiming assistance with customizable settings",
      ar: "مساعدة تصويب احترافية مع إعدادات قابلة للتخصيص",
    },
    price: { egp: 750, usd: 25 },
    image: "/placeholder.svg?height=200&width=300",
    category: { en: "Aiming", ar: "التصويب" },
    type: "hack" as const,
  },
  {
    id: "3",
    name: { en: "Speed Hack", ar: "هاك السرعة" },
    description: {
      en: "Increase movement speed for tactical advantage",
      ar: "زيادة سرعة الحركة للحصول على ميزة تكتيكية",
    },
    price: { egp: 300, usd: 10 },
    image: "/placeholder.svg?height=200&width=300",
    category: { en: "Movement", ar: "الحركة" },
    type: "hack" as const,
  },
  {
    id: "4",
    name: { en: "No Recoil Hack", ar: "هاك بدون ارتداد" },
    description: {
      en: "Eliminate weapon recoil for perfect accuracy",
      ar: "إزالة ارتداد السلاح للحصول على دقة مثالية",
    },
    price: { egp: 600, usd: 20 },
    image: "/placeholder.svg?height=200&width=300",
    category: { en: "Weapon", ar: "السلاح" },
    type: "hack" as const,
  },
  {
    id: "5",
    name: { en: "Radar Hack", ar: "هاك الرادار" },
    description: { en: "See all players on minimap", ar: "رؤية جميع اللاعبين على الخريطة المصغرة" },
    price: { egp: 400, usd: 13 },
    image: "/placeholder.svg?height=200&width=300",
    category: { en: "Vision", ar: "الرؤية" },
    type: "hack" as const,
  },
  {
    id: "6",
    name: { en: "Magic Bullet", ar: "الرصاصة السحرية" },
    description: { en: "Bullets automatically hit targets", ar: "الرصاصات تصيب الأهداف تلقائياً" },
    price: { egp: 900, usd: 30 },
    image: "/placeholder.svg?height=200&width=300",
    category: { en: "Weapon", ar: "السلاح" },
    type: "hack" as const,
  },
]

export default function HacksPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-yellow-400 mb-4">PUBG Hacks</h1>
        <p className="text-gray-300 text-lg">Premium gaming tools and hacks to enhance your PUBG experience.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {hacks.map((hack) => (
          <ProductCard key={hack.id} {...hack} />
        ))}
      </div>
    </div>
  )
}
