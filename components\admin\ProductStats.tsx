"use client"

import { useLanguage } from "@/contexts/LanguageContext"
import { Package, DollarSign, TrendingUp, Users } from "lucide-react"

interface Product {
  id: string
  type: "account" | "uc" | "hack"
  price: { egp: number; usd: number }
}

interface ProductStatsProps {
  products: Product[]
}

export default function ProductStats({ products }: ProductStatsProps) {
  const { t } = useLanguage()

  const stats = {
    total: products.length,
    accounts: products.filter((p) => p.type === "account").length,
    uc: products.filter((p) => p.type === "uc").length,
    hacks: products.filter((p) => p.type === "hack").length,
    totalValueEGP: products.reduce((sum, p) => sum + p.price.egp, 0),
    totalValueUSD: products.reduce((sum, p) => sum + p.price.usd, 0),
    avgPriceEGP:
      products.length > 0 ? Math.round(products.reduce((sum, p) => sum + p.price.egp, 0) / products.length) : 0,
    avgPriceUSD:
      products.length > 0 ? Math.round(products.reduce((sum, p) => sum + p.price.usd, 0) / products.length) : 0,
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-sm">Total Products</p>
            <p className="text-2xl font-bold text-yellow-400">{stats.total}</p>
          </div>
          <Package className="text-yellow-400" size={32} />
        </div>
        <div className="mt-4 text-xs text-gray-500">
          {stats.accounts} Accounts • {stats.uc} UC • {stats.hacks} Hacks
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 border border-green-500/20">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-sm">Total Value (EGP)</p>
            <p className="text-2xl font-bold text-green-400">{stats.totalValueEGP.toLocaleString()}</p>
          </div>
          <DollarSign className="text-green-400" size={32} />
        </div>
        <div className="mt-4 text-xs text-gray-500">Avg: {stats.avgPriceEGP} EGP per product</div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 border border-blue-500/20">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-sm">Total Value (USD)</p>
            <p className="text-2xl font-bold text-blue-400">${stats.totalValueUSD.toLocaleString()}</p>
          </div>
          <TrendingUp className="text-blue-400" size={32} />
        </div>
        <div className="mt-4 text-xs text-gray-500">Avg: ${stats.avgPriceUSD} USD per product</div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 border border-purple-500/20">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-sm">Categories</p>
            <p className="text-2xl font-bold text-purple-400">3</p>
          </div>
          <Users className="text-purple-400" size={32} />
        </div>
        <div className="mt-4 text-xs text-gray-500">Accounts, UC Packages, Hacks</div>
      </div>
    </div>
  )
}
