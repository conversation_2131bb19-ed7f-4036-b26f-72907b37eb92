"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/database';
import { User as UserType, UserRole } from '@/lib/types';
import SimpleLoader from '@/components/SimpleLoader';

interface AuthContextType {
  user: SupabaseUser | null;
  userData: UserType | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateUserData: (data: Partial<UserType>) => Promise<void>;
  clearAuthAndRestart: () => Promise<void>;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  isAdmin: boolean;
  isModerator: boolean;
  canManageProducts: boolean;
  canManageUsers: boolean;
  canManageOrders: boolean;
  canViewAnalytics: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userData: null,
  loading: true,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  updateUserData: async () => {},
  clearAuthAndRestart: async () => {},
  hasRole: () => false,
  hasAnyRole: () => false,
  isAdmin: false,
  isModerator: false,
  canManageProducts: false,
  canManageUsers: false,
  canManageOrders: false,
  canViewAnalytics: false
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [userData, setUserData] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);

  // Clear all authentication data
  const clearAuthData = () => {
    setUser(null);
    setUserData(null);
    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('supabase.auth.token');
      localStorage.clear();
    }
  };

  // Fetch user data from our users table
  const fetchUserData = async (userId: string) => {
    try {
      console.log('Fetching user data for ID:', userId);

      // First, verify the user session is still valid
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session) {
        console.log('Invalid session, clearing auth data');
        clearAuthData();
        return;
      }

      let userData = await DatabaseService.getUser(userId);
      console.log('Initial user data fetch result:', userData);

      // If user doesn't exist in public.users table, create it directly
      if (!userData) {
        console.log('User not found in public.users table, creating new record...');

        if (session.user) {
          const newUserData = {
            id: session.user.id,
            email: session.user.email!,
            display_name: session.user.user_metadata?.display_name || session.user.email!,
            is_email_verified: !!session.user.email_confirmed_at,
            role: 'customer',
            is_active: true,
            total_orders: 0,
            total_spent: 0,
            loyalty_points: 0
          };

          console.log('Inserting new user data:', newUserData);

          // Insert directly into the database with correct column names
          const { error: insertError } = await supabase
            .from('users')
            .insert(newUserData);

          if (insertError) {
            console.error('Error creating user record:', insertError);
            // If it's a duplicate key error, try to fetch the existing user
            if (insertError.code === '23505') {
              console.log('User already exists, fetching existing data...');
              userData = await DatabaseService.getUser(userId);
            } else {
              throw new Error(`Failed to create user record: ${insertError.message}`);
            }
          } else {
            console.log('User record created successfully, fetching updated data...');
            // Fetch the newly created user data
            userData = await DatabaseService.getUser(userId);
            console.log('Newly created user data:', userData);
          }
        } else {
          throw new Error('No authenticated user found in session');
        }
      }

      if (userData) {
        console.log('Setting user data:', userData);
        setUserData(userData);
      } else {
        console.log('No user data found, clearing auth');
        clearAuthData();
      }
    } catch (error) {
      console.error('Error fetching/creating user data:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : 'Unknown',
        code: (error as any)?.code || 'No code'
      });

      // If it's an auth error, clear everything
      if (error instanceof Error && (
        error.message.includes('refresh_token_not_found') ||
        error.message.includes('Invalid Refresh Token') ||
        error.message.includes('JWT')
      )) {
        console.log('Auth error detected, clearing all auth data');
        clearAuthData();
        await supabase.auth.signOut();
      } else {
        // For other errors, just set userData to null
        setUserData(null);
      }
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) throw error;
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, displayName: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          display_name: displayName
        }
      }
    });
    if (error) throw error;
  };

  // Sign out
  const signOut = async () => {
    clearAuthData();
    const { error } = await supabase.auth.signOut();
    if (error) console.error('Sign out error:', error);
    // Force reload to clear any cached state
    if (typeof window !== 'undefined') {
      window.location.href = '/auth';
    }
  };

  // Clear authentication and restart
  const clearAuthAndRestart = async () => {
    console.log('Clearing authentication and restarting...');
    clearAuthData();
    await supabase.auth.signOut();
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  // Update user data
  const updateUserData = async (data: Partial<UserType>) => {
    if (!user) throw new Error('No user logged in');
    
    await DatabaseService.updateUser(user.id, data);
    await fetchUserData(user.id);
  };

  // Permission helper functions
  const hasRole = (role: UserRole): boolean => {
    return userData?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return userData ? roles.includes(userData.role) : false;
  };

  const isAdmin = userData?.role === UserRole.ADMIN || userData?.role === UserRole.SUPER_ADMIN;
  const isModerator = userData?.role === UserRole.MODERATOR || isAdmin;

  // Permission checks
  const canManageProducts = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canManageUsers = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
  const canManageOrders = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canViewAnalytics = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);

  // Listen for auth state changes
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Error getting initial session:', error);
          setUser(null);
          setUserData(null);
        } else {
          setUser(session?.user ?? null);
          if (session?.user) {
            await fetchUserData(session.user.id);
          } else {
            setUserData(null);
          }
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        setUser(null);
        setUserData(null);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event);
        setUser(session?.user ?? null);

        if (session?.user) {
          // For new signups, add a small delay to allow database triggers to complete
          if (event === 'SIGNED_UP') {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          await fetchUserData(session.user.id);
        } else {
          setUserData(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-yellow-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{
      user,
      userData,
      loading,
      signIn,
      signUp,
      signOut,
      updateUserData,
      clearAuthAndRestart,
      hasRole,
      hasAnyRole,
      isAdmin,
      isModerator,
      canManageProducts,
      canManageUsers,
      canManageOrders,
      canViewAnalytics
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
