"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { User as UserType, UserRole } from '@/lib/types';
import SimpleLoader from '@/components/SimpleLoader';

interface AuthContextType {
  user: User | null;
  userData: UserType | null;
  loading: boolean;
  updateUserData: (data: Partial<UserType>) => Promise<void>;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  isAdmin: boolean;
  isModerator: boolean;
  canManageProducts: boolean;
  canManageUsers: boolean;
  canManageOrders: boolean;
  canViewAnalytics: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userData: null,
  loading: true,
  updateUserData: async () => {},
  hasRole: () => false,
  hasAnyRole: () => false,
  isAdmin: false,
  isModerator: false,
  canManageProducts: false,
  canManageUsers: false,
  canManageOrders: false,
  canViewAnalytics: false
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);

  // Function to fetch or create user data
  const fetchOrCreateUserData = async (user: User) => {
    const userRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      // Update last login
      const data = userDoc.data() as UserType;
      const updatedData = { ...data, lastLoginAt: new Date().toISOString() };
      await setDoc(userRef, updatedData, { merge: true });
      setUserData(updatedData);
    } else {
      // Create new user data
      const newUserData: Omit<UserType, 'id'> = {
        email: user.email || '',
        displayName: user.displayName || user.email?.split('@')[0] || '',
        photoURL: user.photoURL || '',
        role: UserRole.CUSTOMER,
        isActive: true,
        isEmailVerified: user.emailVerified,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
        totalOrders: 0,
        totalSpent: 0,
        loyaltyPoints: 0
      };
      await setDoc(userRef, newUserData);
      setUserData({ id: user.uid, ...newUserData });
    }
  };

  // Function to update user data
  const updateUserData = async (data: Partial<UserType>) => {
    if (!user) return;

    const userRef = doc(db, 'users', user.uid);
    const updatedData = {
      ...data,
      updatedAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };
    await setDoc(userRef, updatedData, { merge: true });
    setUserData(prev => prev ? { ...prev, ...updatedData } : null);
  };

  // Role-based access control functions
  const hasRole = (role: UserRole): boolean => {
    return userData?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return userData ? roles.includes(userData.role) : false;
  };

  const isAdmin = userData?.role === UserRole.ADMIN || userData?.role === UserRole.SUPER_ADMIN;
  const isModerator = userData?.role === UserRole.MODERATOR || isAdmin;

  // Permission checks
  const canManageProducts = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canManageUsers = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
  const canManageOrders = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canViewAnalytics = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      if (user) {
        await fetchOrCreateUserData(user);
      } else {
        setUserData(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  if (loading) {
    return <SimpleLoader />;
  }

  return (
    <AuthContext.Provider value={{
      user,
      userData,
      loading,
      updateUserData,
      hasRole,
      hasAnyRole,
      isAdmin,
      isModerator,
      canManageProducts,
      canManageUsers,
      canManageOrders,
      canViewAnalytics
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext); 