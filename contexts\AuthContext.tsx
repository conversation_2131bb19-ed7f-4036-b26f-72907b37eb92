"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/database';
import { User as UserType, UserRole } from '@/lib/types';
import SimpleLoader from '@/components/SimpleLoader';

interface AuthContextType {
  user: SupabaseUser | null;
  userData: UserType | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateUserData: (data: Partial<UserType>) => Promise<void>;
  clearAuthAndRestart: () => Promise<void>;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  isAdmin: boolean;
  isModerator: boolean;
  canManageProducts: boolean;
  canManageUsers: boolean;
  canManageOrders: boolean;
  canViewAnalytics: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userData: null,
  loading: true,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  updateUserData: async () => {},
  clearAuthAndRestart: async () => {},
  hasRole: () => false,
  hasAnyRole: () => false,
  isAdmin: false,
  isModerator: false,
  canManageProducts: false,
  canManageUsers: false,
  canManageOrders: false,
  canViewAnalytics: false
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [userData, setUserData] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);

  // Clear all authentication data
  const clearAuthData = () => {
    setUser(null);
    setUserData(null);
    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('supabase.auth.token');
      localStorage.clear();
    }
  };

  // Fetch user data from our users table
  const fetchUserData = async (userId: string) => {
    try {
      console.log('Fetching user data for ID:', userId);

      // Simple approach: just try to get the user data
      let userData = await DatabaseService.getUser(userId);
      console.log('User data fetch result:', userData);

      // If user doesn't exist, create a simple record
      if (!userData) {
        console.log('User not found, creating simple record...');

        // Get current session for user info
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          const newUserData = {
            id: session.user.id,
            email: session.user.email!,
            display_name: session.user.email!.split('@')[0],
            is_email_verified: !!session.user.email_confirmed_at,
            role: 'customer',
            is_active: true,
            total_orders: 0,
            total_spent: 0,
            loyalty_points: 0
          };

          // Try to insert, but don't fail if it already exists
          const { error: insertError } = await supabase
            .from('users')
            .insert(newUserData);

          if (insertError && insertError.code !== '23505') {
            console.error('Error creating user record:', insertError);
            // Don't throw, just continue with null userData
          }

          // Try to fetch again
          userData = await DatabaseService.getUser(userId);
        }
      }

      // Set the user data (even if null)
      setUserData(userData);
      console.log('User data set successfully');

    } catch (error) {
      console.error('Error in fetchUserData:', error);
      // Don't throw errors, just set userData to null and continue
      setUserData(null);
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) throw error;
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, displayName: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          display_name: displayName
        }
      }
    });
    if (error) throw error;
  };

  // Sign out
  const signOut = async () => {
    clearAuthData();
    const { error } = await supabase.auth.signOut();
    if (error) console.error('Sign out error:', error);
    // Force reload to clear any cached state
    if (typeof window !== 'undefined') {
      window.location.href = '/auth';
    }
  };

  // Clear authentication and restart
  const clearAuthAndRestart = async () => {
    console.log('Clearing authentication and restarting...');
    clearAuthData();
    await supabase.auth.signOut();
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  // Update user data
  const updateUserData = async (data: Partial<UserType>) => {
    if (!user) {
      throw new Error('No user logged in');
    }

    try {
      console.log('Updating user data:', data);

      // Update in database
      await DatabaseService.updateUser(user.id, data);
      console.log('User data updated in database successfully');

      // Refresh user data from database
      await fetchUserData(user.id);
      console.log('User data refreshed successfully');

    } catch (error) {
      console.error('Error updating user data:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to update user data');
    }
  };

  // Permission helper functions
  const hasRole = (role: UserRole): boolean => {
    return userData?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return userData ? roles.includes(userData.role) : false;
  };

  const isAdmin = userData?.role === UserRole.ADMIN || userData?.role === UserRole.SUPER_ADMIN;
  const isModerator = userData?.role === UserRole.MODERATOR || isAdmin;

  // Permission checks
  const canManageProducts = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canManageUsers = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
  const canManageOrders = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canViewAnalytics = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);

  // Listen for auth state changes
  useEffect(() => {
    let mounted = true;

    // Set a timeout to ensure loading doesn't get stuck
    const loadingTimeout = setTimeout(() => {
      if (mounted) {
        console.log('Loading timeout reached, setting loading to false');
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('Getting initial session...');
        const { data: { session }, error } = await supabase.auth.getSession();

        if (!mounted) return;

        if (error) {
          console.error('Error getting initial session:', error);
          setUser(null);
          setUserData(null);
          setLoading(false);
          return;
        }

        console.log('Initial session:', session?.user?.email || 'No user');
        setUser(session?.user ?? null);

        if (session?.user) {
          try {
            await fetchUserData(session.user.id);
          } catch (fetchError) {
            console.error('Error fetching user data:', fetchError);
            // Continue anyway, don't block the app
          }
        } else {
          setUserData(null);
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        if (mounted) {
          setUser(null);
          setUserData(null);
        }
      } finally {
        if (mounted) {
          clearTimeout(loadingTimeout);
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        console.log('Auth state changed:', event, session?.user?.email || 'No user');
        setUser(session?.user ?? null);

        if (session?.user) {
          try {
            // For new signups, add a small delay to allow database triggers to complete
            if (event === 'SIGNED_UP') {
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
            await fetchUserData(session.user.id);
          } catch (fetchError) {
            console.error('Error fetching user data in auth change:', fetchError);
            // Continue anyway, don't block the app
          }
        } else {
          setUserData(null);
        }

        if (mounted) {
          setLoading(false);
        }
      }
    );

    return () => {
      mounted = false;
      clearTimeout(loadingTimeout);
      subscription.unsubscribe();
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-yellow-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{
      user,
      userData,
      loading,
      signIn,
      signUp,
      signOut,
      updateUserData,
      clearAuthAndRestart,
      hasRole,
      hasAnyRole,
      isAdmin,
      isModerator,
      canManageProducts,
      canManageUsers,
      canManageOrders,
      canViewAnalytics
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
