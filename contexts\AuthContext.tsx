"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/database';
import { User as UserType, UserRole } from '@/lib/types';
import SimpleLoader from '@/components/SimpleLoader';

interface AuthContextType {
  user: SupabaseUser | null;
  userData: UserType | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateUserData: (data: Partial<UserType>) => Promise<void>;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  isAdmin: boolean;
  isModerator: boolean;
  canManageProducts: boolean;
  canManageUsers: boolean;
  canManageOrders: boolean;
  canViewAnalytics: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userData: null,
  loading: true,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  updateUserData: async () => {},
  hasRole: () => false,
  hasAnyRole: () => false,
  isAdmin: false,
  isModerator: false,
  canManageProducts: false,
  canManageUsers: false,
  canManageOrders: false,
  canViewAnalytics: false
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [userData, setUserData] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch user data from our users table
  const fetchUserData = async (userId: string) => {
    try {
      console.log('Fetching user data for ID:', userId);
      let userData = await DatabaseService.getUser(userId);
      console.log('Initial user data fetch result:', userData);

      // If user doesn't exist in public.users table, create it directly
      if (!userData) {
        console.log('User not found in public.users table, creating new record...');
        const { data: authUser } = await supabase.auth.getUser();
        console.log('Auth user data:', authUser.user);

        if (authUser.user) {
          const newUserData = {
            id: authUser.user.id,
            email: authUser.user.email!,
            display_name: authUser.user.user_metadata?.display_name || authUser.user.email!,
            is_email_verified: !!authUser.user.email_confirmed_at,
            role: 'customer',
            is_active: true,
            total_orders: 0,
            total_spent: 0,
            loyalty_points: 0
          };

          console.log('Inserting new user data:', newUserData);

          // Insert directly into the database with correct column names
          const { error: insertError } = await supabase
            .from('users')
            .insert(newUserData);

          if (insertError) {
            console.error('Error creating user record:', insertError);
            throw new Error(`Failed to create user record: ${insertError.message}`);
          } else {
            console.log('User record created successfully, fetching updated data...');
            // Fetch the newly created user data
            userData = await DatabaseService.getUser(userId);
            console.log('Newly created user data:', userData);

            if (!userData) {
              throw new Error('Failed to fetch newly created user data');
            }
          }
        } else {
          throw new Error('No authenticated user found');
        }
      }

      console.log('Setting user data:', userData);
      setUserData(userData);
    } catch (error) {
      console.error('Error fetching/creating user data:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        error: error
      });
      // Set userData to null on error to prevent infinite loops
      setUserData(null);
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) throw error;
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, displayName: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          display_name: displayName
        }
      }
    });
    if (error) throw error;
  };

  // Sign out
  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  // Update user data
  const updateUserData = async (data: Partial<UserType>) => {
    if (!user) throw new Error('No user logged in');
    
    await DatabaseService.updateUser(user.id, data);
    await fetchUserData(user.id);
  };

  // Permission helper functions
  const hasRole = (role: UserRole): boolean => {
    return userData?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return userData ? roles.includes(userData.role) : false;
  };

  const isAdmin = userData?.role === UserRole.ADMIN || userData?.role === UserRole.SUPER_ADMIN;
  const isModerator = userData?.role === UserRole.MODERATOR || isAdmin;

  // Permission checks
  const canManageProducts = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canManageUsers = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
  const canManageOrders = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR]);
  const canViewAnalytics = hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);

  // Listen for auth state changes
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);

        if (session?.user) {
          // For new signups, add a small delay to allow database triggers to complete
          if (event === 'SIGNED_UP') {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          await fetchUserData(session.user.id);
        } else {
          setUserData(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  if (loading) {
    return <SimpleLoader />;
  }

  return (
    <AuthContext.Provider value={{
      user,
      userData,
      loading,
      signIn,
      signUp,
      signOut,
      updateUserData,
      hasRole,
      hasAnyRole,
      isAdmin,
      isModerator,
      canManageProducts,
      canManageUsers,
      canManageOrders,
      canViewAnalytics
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
