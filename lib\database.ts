// Supabase database service
import { supabase } from './supabase';
import {
  Product,
  User,
  Order,
  ProductFilters,
  OrderFilters,
  UserFilters,
  PaginatedResponse,
  DashboardStats,
  ProductType,
  OrderStatus,
  UserRole
} from './types';

// Table names for Supabase
export const TABLES = {
  PRODUCTS: 'products',
  USERS: 'users',
  ORDERS: 'orders',
  ANALYTICS: 'analytics',
  SETTINGS: 'settings'
} as const;

// Database service class - will be implemented with Supabase
export class DatabaseService {
  // Product operations
  static async createProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS)
      .insert(product)
      .select('id')
      .single();

    if (error) throw error;
    return data.id;
  }

  static async getProduct(id: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  static async updateProduct(id: string, updates: Partial<Product>): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PRODUCTS)
      .update(updates)
      .eq('id', id);

    if (error) throw error;
  }

  static async deleteProduct(id: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PRODUCTS)
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async getProducts(filters?: ProductFilters): Promise<PaginatedResponse<Product>> {
    let query = supabase.from(TABLES.PRODUCTS).select('*', { count: 'exact' });

    // Apply filters
    if (filters?.type) {
      query = query.eq('type', filters.type);
    }
    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }
    if (filters?.isFeatured !== undefined) {
      query = query.eq('is_featured', filters.isFeatured);
    }
    if (filters?.category) {
      query = query.contains('category', { en: filters.category });
    }
    if (filters?.minPrice !== undefined) {
      query = query.gte('price->egp', filters.minPrice);
    }
    if (filters?.maxPrice !== undefined) {
      query = query.lte('price->egp', filters.maxPrice);
    }

    // Apply pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    // Apply sorting
    if (filters?.sortBy) {
      const order = filters.sortOrder || 'asc';
      query = query.order(filters.sortBy, { ascending: order === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  // User operations
  static async createUser(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert(user)
      .select('id')
      .single();

    if (error) throw error;
    return data.id;
  }

  static async getUser(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  static async updateUser(id: string, updates: Partial<User>): Promise<void> {
    const { error } = await supabase
      .from(TABLES.USERS)
      .update(updates)
      .eq('id', id);

    if (error) throw error;
  }

  static async getUsers(filters?: UserFilters): Promise<PaginatedResponse<User>> {
    let query = supabase.from(TABLES.USERS).select('*', { count: 'exact' });

    // Apply filters
    if (filters?.role) {
      query = query.eq('role', filters.role);
    }
    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }
    if (filters?.search) {
      query = query.or(`display_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
    }

    // Apply pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    // Apply sorting
    if (filters?.sortBy) {
      const order = filters.sortOrder || 'asc';
      query = query.order(filters.sortBy, { ascending: order === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  // Order operations
  static async createOrder(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const { data, error } = await supabase
      .from(TABLES.ORDERS)
      .insert(order)
      .select('id')
      .single();

    if (error) throw error;
    return data.id;
  }

  static async getOrder(id: string): Promise<Order | null> {
    const { data, error } = await supabase
      .from(TABLES.ORDERS)
      .select(`
        *,
        user:users(*),
        product:products(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  static async updateOrder(id: string, updates: Partial<Order>): Promise<void> {
    const { error } = await supabase
      .from(TABLES.ORDERS)
      .update(updates)
      .eq('id', id);

    if (error) throw error;
  }

  static async getOrders(filters?: OrderFilters): Promise<PaginatedResponse<Order>> {
    let query = supabase
      .from(TABLES.ORDERS)
      .select(`
        *,
        user:users(id, display_name, email),
        product:products(id, name, type)
      `, { count: 'exact' });

    // Apply filters
    if (filters?.userId) {
      query = query.eq('user_id', filters.userId);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.paymentStatus) {
      query = query.eq('payment_status', filters.paymentStatus);
    }

    // Apply pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    // Apply sorting
    if (filters?.sortBy) {
      const order = filters.sortOrder || 'asc';
      query = query.order(filters.sortBy, { ascending: order === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  // Analytics
  static async getDashboardStats(): Promise<DashboardStats> {
    const [
      { count: totalProducts },
      { count: totalUsers },
      { count: totalOrders },
      { data: revenueData },
      { count: pendingOrders },
      { count: newUsersToday }
    ] = await Promise.all([
      supabase.from(TABLES.PRODUCTS).select('*', { count: 'exact', head: true }),
      supabase.from(TABLES.USERS).select('*', { count: 'exact', head: true }),
      supabase.from(TABLES.ORDERS).select('*', { count: 'exact', head: true }),
      supabase.from(TABLES.ORDERS)
        .select('total_amount')
        .eq('payment_status', 'paid'),
      supabase.from(TABLES.ORDERS)
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending'),
      supabase.from(TABLES.USERS)
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date().toISOString().split('T')[0])
    ]);

    const totalRevenue = revenueData?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0;

    // Get low stock products
    const { count: lowStockProducts } = await supabase
      .from(TABLES.PRODUCTS)
      .select('*', { count: 'exact', head: true })
      .lt('stock_count', 'low_stock_threshold');

    return {
      totalProducts: totalProducts || 0,
      totalUsers: totalUsers || 0,
      totalOrders: totalOrders || 0,
      totalRevenue,
      lowStockProducts: lowStockProducts || 0,
      pendingOrders: pendingOrders || 0,
      newUsersToday: newUsersToday || 0,
      salesGrowth: 0 // TODO: Calculate based on historical data
    };
  }

  // File operations
  static async uploadFile(file: File, path: string): Promise<string> {
    const { data, error } = await supabase.storage
      .from('uploads')
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) throw error;

    const { data: urlData } = supabase.storage
      .from('uploads')
      .getPublicUrl(data.path);

    return urlData.publicUrl;
  }

  static async deleteFile(path: string): Promise<void> {
    const { error } = await supabase.storage
      .from('uploads')
      .remove([path]);

    if (error) throw error;
  }
}

  static async getProduct(id: string): Promise<Product | null> {
    const docRef = doc(db, COLLECTIONS.PRODUCTS, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Product;
    }
    return null;
  }

  static async updateProduct(id: string, updates: Partial<Product>): Promise<void> {
    const docRef = doc(db, COLLECTIONS.PRODUCTS, id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  }

  static async deleteProduct(id: string): Promise<void> {
    const docRef = doc(db, COLLECTIONS.PRODUCTS, id);
    await deleteDoc(docRef);
  }

  static async getProducts(
    filters: ProductFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<Product>> {
    const constraints: QueryConstraint[] = [];
    
    if (filters.type) {
      constraints.push(where('type', '==', filters.type));
    }
    if (filters.isActive !== undefined) {
      constraints.push(where('isActive', '==', filters.isActive));
    }
    if (filters.isFeatured !== undefined) {
      constraints.push(where('isFeatured', '==', filters.isFeatured));
    }
    if (filters.inStock) {
      constraints.push(where('stockCount', '>', 0));
    }
    if (filters.category) {
      constraints.push(where('category.en', '==', filters.category));
    }

    constraints.push(orderBy('createdAt', 'desc'));
    constraints.push(limit(pageSize));

    const q = query(collection(db, COLLECTIONS.PRODUCTS), ...constraints);
    const querySnapshot = await getDocs(q);
    
    const products: Product[] = [];
    querySnapshot.forEach((doc) => {
      products.push({ id: doc.id, ...doc.data() } as Product);
    });

    // Get total count (simplified - in production, use a separate count collection)
    const totalQuery = query(collection(db, COLLECTIONS.PRODUCTS));
    const totalSnapshot = await getDocs(totalQuery);
    const total = totalSnapshot.size;

    return {
      data: products,
      total,
      page,
      limit: pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // User operations
  static async createUser(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date().toISOString();
    const userData = {
      ...user,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.USERS), userData);
    return docRef.id;
  }

  static async getUser(id: string): Promise<User | null> {
    const docRef = doc(db, COLLECTIONS.USERS, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as User;
    }
    return null;
  }

  static async updateUser(id: string, updates: Partial<User>): Promise<void> {
    const docRef = doc(db, COLLECTIONS.USERS, id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  }

  static async getUsers(
    filters: UserFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<User>> {
    const constraints: QueryConstraint[] = [];
    
    if (filters.role) {
      constraints.push(where('role', '==', filters.role));
    }
    if (filters.isActive !== undefined) {
      constraints.push(where('isActive', '==', filters.isActive));
    }
    if (filters.country) {
      constraints.push(where('country', '==', filters.country));
    }

    constraints.push(orderBy('createdAt', 'desc'));
    constraints.push(limit(pageSize));

    const q = query(collection(db, COLLECTIONS.USERS), ...constraints);
    const querySnapshot = await getDocs(q);
    
    const users: User[] = [];
    querySnapshot.forEach((doc) => {
      users.push({ id: doc.id, ...doc.data() } as User);
    });

    const totalQuery = query(collection(db, COLLECTIONS.USERS));
    const totalSnapshot = await getDocs(totalQuery);
    const total = totalSnapshot.size;

    return {
      data: users,
      total,
      page,
      limit: pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // Order operations
  static async createOrder(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date().toISOString();
    const orderData = {
      ...order,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.ORDERS), orderData);
    return docRef.id;
  }

  static async getOrder(id: string): Promise<Order | null> {
    const docRef = doc(db, COLLECTIONS.ORDERS, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Order;
    }
    return null;
  }

  static async updateOrder(id: string, updates: Partial<Order>): Promise<void> {
    const docRef = doc(db, COLLECTIONS.ORDERS, id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  }

  static async getOrders(
    filters: OrderFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<Order>> {
    const constraints: QueryConstraint[] = [];
    
    if (filters.status) {
      constraints.push(where('status', '==', filters.status));
    }
    if (filters.paymentStatus) {
      constraints.push(where('paymentStatus', '==', filters.paymentStatus));
    }
    if (filters.customerId) {
      constraints.push(where('customerId', '==', filters.customerId));
    }

    constraints.push(orderBy('createdAt', 'desc'));
    constraints.push(limit(pageSize));

    const q = query(collection(db, COLLECTIONS.ORDERS), ...constraints);
    const querySnapshot = await getDocs(q);
    
    const orders: Order[] = [];
    querySnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as Order);
    });

    const totalQuery = query(collection(db, COLLECTIONS.ORDERS));
    const totalSnapshot = await getDocs(totalQuery);
    const total = totalSnapshot.size;

    return {
      data: orders,
      total,
      page,
      limit: pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // Dashboard statistics
  static async getDashboardStats(): Promise<DashboardStats> {
    const [productsSnap, usersSnap, ordersSnap] = await Promise.all([
      getDocs(collection(db, COLLECTIONS.PRODUCTS)),
      getDocs(collection(db, COLLECTIONS.USERS)),
      getDocs(collection(db, COLLECTIONS.ORDERS))
    ]);

    const products = productsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as Product));
    const orders = ordersSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as Order));

    const totalRevenue = orders
      .filter(order => order.status === OrderStatus.COMPLETED)
      .reduce((sum, order) => ({
        egp: sum.egp + order.total.egp,
        usd: sum.usd + order.total.usd
      }), { egp: 0, usd: 0 });

    const lowStockProducts = products.filter(p => p.stockCount <= p.lowStockThreshold).length;
    const pendingOrders = orders.filter(o => o.status === OrderStatus.PENDING).length;

    const today = new Date().toISOString().split('T')[0];
    const newUsersToday = usersSnap.docs.filter(doc => {
      const userData = doc.data() as User;
      return userData.createdAt.startsWith(today);
    }).length;

    return {
      totalProducts: productsSnap.size,
      totalUsers: usersSnap.size,
      totalOrders: ordersSnap.size,
      totalRevenue,
      lowStockProducts,
      pendingOrders,
      newUsersToday,
      salesGrowth: 0 // Calculate based on historical data
    };
  }

  // File upload operations
  static async uploadFile(file: File, path: string): Promise<string> {
    const storageRef = ref(storage, path);
    const snapshot = await uploadBytes(storageRef, file);
    return await getDownloadURL(snapshot.ref);
  }

  static async deleteFile(path: string): Promise<void> {
    const storageRef = ref(storage, path);
    await deleteObject(storageRef);
  }

  // Bulk operations
  static async bulkUpdateProducts(updates: { id: string; data: Partial<Product> }[]): Promise<void> {
    const batch = writeBatch(db);
    
    updates.forEach(({ id, data }) => {
      const docRef = doc(db, COLLECTIONS.PRODUCTS, id);
      batch.update(docRef, { ...data, updatedAt: new Date().toISOString() });
    });

    await batch.commit();
  }

  static async bulkDeleteProducts(ids: string[]): Promise<void> {
    const batch = writeBatch(db);
    
    ids.forEach(id => {
      const docRef = doc(db, COLLECTIONS.PRODUCTS, id);
      batch.delete(docRef);
    });

    await batch.commit();
  }

  // Real-time listeners
  static subscribeToProducts(callback: (products: Product[]) => void) {
    const q = query(collection(db, COLLECTIONS.PRODUCTS), orderBy('createdAt', 'desc'));
    
    return onSnapshot(q, (querySnapshot) => {
      const products: Product[] = [];
      querySnapshot.forEach((doc) => {
        products.push({ id: doc.id, ...doc.data() } as Product);
      });
      callback(products);
    });
  }

  static subscribeToOrders(callback: (orders: Order[]) => void) {
    const q = query(collection(db, COLLECTIONS.ORDERS), orderBy('createdAt', 'desc'));
    
    return onSnapshot(q, (querySnapshot) => {
      const orders: Order[] = [];
      querySnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order);
      });
      callback(orders);
    });
  }
}
