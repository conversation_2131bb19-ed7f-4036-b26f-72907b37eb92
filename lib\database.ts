// Supabase database service
import { supabase } from './supabase';
import {
  Product,
  User,
  Order,
  ProductFilters,
  OrderFilters,
  UserFilters,
  PaginatedResponse,
  DashboardStats,
  ProductType,
  OrderStatus,
  UserRole
} from './types';

// Table names for Supabase
export const TABLES = {
  PRODUCTS: 'products',
  USERS: 'users', 
  ORDERS: 'orders',
  ANALYTICS: 'analytics',
  SETTINGS: 'settings'
} as const;

// Database service class
export class DatabaseService {
  // Product operations
  static async createProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS)
      .insert(product)
      .select('id')
      .single();

    if (error) throw error;
    return data.id;
  }

  static async getProduct(id: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  static async updateProduct(id: string, updates: Partial<Product>): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PRODUCTS)
      .update(updates)
      .eq('id', id);

    if (error) throw error;
  }

  static async deleteProduct(id: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PRODUCTS)
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async getProducts(filters?: ProductFilters): Promise<PaginatedResponse<Product>> {
    let query = supabase.from(TABLES.PRODUCTS).select('*', { count: 'exact' });

    // Apply filters
    if (filters?.type) {
      query = query.eq('type', filters.type);
    }
    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }
    if (filters?.isFeatured !== undefined) {
      query = query.eq('is_featured', filters.isFeatured);
    }
    if (filters?.category) {
      query = query.contains('category', { en: filters.category });
    }
    if (filters?.minPrice !== undefined) {
      query = query.gte('price->egp', filters.minPrice);
    }
    if (filters?.maxPrice !== undefined) {
      query = query.lte('price->egp', filters.maxPrice);
    }

    // Apply pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    // Apply sorting
    if (filters?.sortBy) {
      const order = filters.sortOrder || 'asc';
      query = query.order(filters.sortBy, { ascending: order === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  // User operations
  static async createUser(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert(user)
      .select('id')
      .single();

    if (error) throw error;
    return data.id;
  }

  static async getUser(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  static async updateUser(id: string, updates: Partial<User>): Promise<void> {
    const { error } = await supabase
      .from(TABLES.USERS)
      .update(updates)
      .eq('id', id);

    if (error) throw error;
  }

  static async getUsers(filters?: UserFilters): Promise<PaginatedResponse<User>> {
    let query = supabase.from(TABLES.USERS).select('*', { count: 'exact' });

    // Apply filters
    if (filters?.role) {
      query = query.eq('role', filters.role);
    }
    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }
    if (filters?.search) {
      query = query.or(`display_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
    }

    // Apply pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    // Apply sorting
    if (filters?.sortBy) {
      const order = filters.sortOrder || 'asc';
      query = query.order(filters.sortBy, { ascending: order === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  // Order operations
  static async createOrder(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const { data, error } = await supabase
      .from(TABLES.ORDERS)
      .insert(order)
      .select('id')
      .single();

    if (error) throw error;
    return data.id;
  }

  static async getOrder(id: string): Promise<Order | null> {
    const { data, error } = await supabase
      .from(TABLES.ORDERS)
      .select(`
        *,
        user:users(*),
        product:products(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  static async updateOrder(id: string, updates: Partial<Order>): Promise<void> {
    const { error } = await supabase
      .from(TABLES.ORDERS)
      .update(updates)
      .eq('id', id);

    if (error) throw error;
  }

  static async getOrders(filters?: OrderFilters): Promise<PaginatedResponse<Order>> {
    let query = supabase
      .from(TABLES.ORDERS)
      .select(`
        *,
        user:users(id, display_name, email),
        product:products(id, name, type)
      `, { count: 'exact' });

    // Apply filters
    if (filters?.userId) {
      query = query.eq('user_id', filters.userId);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.paymentStatus) {
      query = query.eq('payment_status', filters.paymentStatus);
    }

    // Apply pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    // Apply sorting
    if (filters?.sortBy) {
      const order = filters.sortOrder || 'asc';
      query = query.order(filters.sortBy, { ascending: order === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  // Analytics
  static async getDashboardStats(): Promise<DashboardStats> {
    const [
      { count: totalProducts },
      { count: totalUsers },
      { count: totalOrders },
      { data: revenueData },
      { count: pendingOrders },
      { count: newUsersToday }
    ] = await Promise.all([
      supabase.from(TABLES.PRODUCTS).select('*', { count: 'exact', head: true }),
      supabase.from(TABLES.USERS).select('*', { count: 'exact', head: true }),
      supabase.from(TABLES.ORDERS).select('*', { count: 'exact', head: true }),
      supabase.from(TABLES.ORDERS)
        .select('total_amount')
        .eq('payment_status', 'paid'),
      supabase.from(TABLES.ORDERS)
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending'),
      supabase.from(TABLES.USERS)
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date().toISOString().split('T')[0])
    ]);

    const totalRevenue = revenueData?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0;

    return {
      totalProducts: totalProducts || 0,
      totalUsers: totalUsers || 0,
      totalOrders: totalOrders || 0,
      totalRevenue,
      lowStockProducts: 0,
      pendingOrders: pendingOrders || 0,
      newUsersToday: newUsersToday || 0,
      salesGrowth: 0
    };
  }

  // File operations
  static async uploadFile(file: File, path: string): Promise<string> {
    const { data, error } = await supabase.storage
      .from('uploads')
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) throw error;

    const { data: urlData } = supabase.storage
      .from('uploads')
      .getPublicUrl(data.path);

    return urlData.publicUrl;
  }

  static async deleteFile(path: string): Promise<void> {
    const { error } = await supabase.storage
      .from('uploads')
      .remove([path]);

    if (error) throw error;
  }
}
