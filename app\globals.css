@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 90%;

    --card: 0 0% 13%;
    --card-foreground: 0 0% 90%;

    --popover: 0 0% 13%;
    --popover-foreground: 0 0% 90%;

    --primary: 35 100% 50%;
    --primary-foreground: 0 0% 10%;

    --secondary: 0 0% 53%;
    --secondary-foreground: 0 0% 0%;

    --muted: 0 0% 13%;
    --muted-foreground: 0 0% 63%;

    --accent: 35 100% 50%;
    --accent-foreground: 0 0% 10%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 35 100% 50%;

    --radius: 0.75rem;
    
    /* Global site colors that can be referenced everywhere */
    --pubg-orange-color: #F2A900;
    --pubg-black-color: #000000;
    --pubg-darkGray-color: #222222;
    --pubg-gray-color: #888888;
    --pubg-lightGray-color: #E5E5E5;
    
    /* Colors with opacity variants */
    --pubg-orange-10: rgba(242, 169, 0, 0.1);
    --pubg-orange-20: rgba(242, 169, 0, 0.2);
    --pubg-orange-30: rgba(242, 169, 0, 0.3);
    --pubg-orange-50: rgba(242, 169, 0, 0.5);
    --pubg-orange-80: rgba(242, 169, 0, 0.8);
    
    --pubg-gray-10: rgba(136, 136, 136, 0.1);
    --pubg-gray-20: rgba(136, 136, 136, 0.2);
    --pubg-gray-30: rgba(136, 136, 136, 0.3);
    --pubg-gray-50: rgba(136, 136, 136, 0.5);
    
    /* Semi-transparent background colors for cards, inputs, etc. */
    --bg-card-dark: rgba(0, 0, 0, 0.95);
    --bg-input-dark: rgba(20, 20, 20, 0.8);
    --bg-input-hover: rgba(30, 30, 30, 0.9);
    --bg-section-item: rgba(20, 20, 20, 0.9);
    --bg-section-item-hover: rgba(30, 30, 30, 0.95);
    
    /* Border colors */
    --border-gray: rgba(50, 50, 50, 0.7);
    --border-light: rgba(50, 50, 50, 0.4);
    --border-orange: rgba(242, 169, 0, 0.5);

    --font-inter: 'Inter', sans-serif;
    --font-cairo: 'Cairo', sans-serif;
  }

  * {
    @apply border-border;
    font-family: 'Cairo', sans-serif !important;
  }
  
  html {
    direction: rtl;
    font-family: 'Cairo', sans-serif !important;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: 'Cairo', sans-serif !important;
    overflow-x: hidden;
  }

  /* Simplify by forcing Cairo everywhere, we'll handle English text later if needed */
  [dir="rtl"] *,
  [dir="ltr"] *,
  [lang="ar"],
  *:lang(ar),
  button,
  input,
  select,
  textarea,
  h1, h2, h3, h4, h5, h6,
  p, span, div {
    font-family: 'Cairo', sans-serif !important;
  }

  ::selection {
    @apply bg-[var(--pubg-orange-30)] text-white;
  }
}

@layer components {
  .glass-card {
    @apply bg-card/80 backdrop-blur-md border border-white/10 shadow-xl;
  }
  
  .section-title {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 text-white relative inline-block after:content-[''] after:absolute after:w-1/2 after:h-1 after:bg-pubg-orange after:bottom-0 after:right-0;
  }
  
  .button-primary {
    @apply bg-pubg-orange text-pubg-black font-bold px-4 py-2 md:px-6 md:py-3 text-sm md:text-base rounded-lg transition-all duration-300 hover:bg-pubg-orange/90 hover:translate-y-[-2px] shadow-lg hover:shadow-pubg-orange/30 active:translate-y-0;
  }
  
  .button-secondary {
    @apply bg-pubg-gray text-white font-bold px-4 py-2 md:px-6 md:py-3 text-sm md:text-base rounded-lg transition-all duration-300 hover:bg-pubg-gray/90 hover:translate-y-[-2px] shadow-lg hover:shadow-pubg-gray/30 active:translate-y-0;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:translate-y-[-5px] hover:shadow-xl;
  }

  .responsive-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6;
  }
  
  .responsive-padding {
    @apply py-6 md:py-8 lg:py-12;
  }
}

@layer utilities {
  /* Hardware acceleration for better performance */
  .hardware-accelerated {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
  }
  
  .will-change-transform {
    will-change: transform;
  }
  
  /* Optimized animations */
  .smooth-animation {
    animation-fill-mode: both;
    animation-duration: 0.3s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .text-balance {
    text-wrap: balance;
  }

  .responsive-heading-xl {
    @apply text-2xl md:text-3xl lg:text-4xl xl:text-5xl;
  }

  .responsive-heading-lg {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  .responsive-heading-md {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  .responsive-heading-sm {
    @apply text-base md:text-lg lg:text-xl;
  }

  .responsive-text {
    @apply text-sm md:text-base;
  }
}

/* Critical above-the-fold styles */
.above-fold {
  min-height: 100vh;
  background: linear-gradient(to right, #111827, #000000, #111827);
}

/* RTL Support */
.rtl {
  direction: rtl;
}

.rtl .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.rtl .space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Custom scrollbar for horizontal scroll */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.3);
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(234, 179, 8, 0.6);
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(234, 179, 8, 0.8);
}

/* Auth page specific styles */
@media (prefers-reduced-motion: reduce) {
  .auth-page-background * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.auth-page-background {
  min-height: 100vh;
  background: radial-gradient(ellipse at center, rgba(242, 169, 0, 0.05) 0%, rgba(0, 0, 0, 0.9) 50%, rgba(0, 0, 0, 1) 100%);
  position: relative;
  overflow: hidden;
}

@keyframes float-up-down {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(242, 169, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(242, 169, 0, 0.6);
    transform: scale(1.02);
  }
}

.form-input-glowing:focus {
  box-shadow: 
    0 0 0 2px rgba(242, 169, 0, 0.2),
    0 0 10px rgba(242, 169, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(242, 169, 0, 0.7);
}

.form-button-glowing {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #F2A900 0%, #E67E22 100%);
}

.form-button-glowing:after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: rotate(45deg);
  transition: all 0.5s;
  opacity: 0;
}

.form-button-glowing:hover:after {
  animation: button-shine 0.5s ease-in-out;
}

@keyframes button-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

.tab-indicator {
  position: relative;
  overflow: hidden;
}

.tab-indicator::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, #F2A900, #E67E22);
  transition: all 0.3s ease;
}

.spinner-loading {
  animation: spinner-rotate 1s linear infinite;
}

@keyframes spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Gaming glow effects */
.glow-yellow {
  box-shadow: 0 0 20px rgba(242, 169, 0, 0.3);
}

.glow-yellow:hover {
  box-shadow: 0 0 30px rgba(242, 169, 0, 0.5);
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Language Switcher Styles */
.neo-lang-switcher {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.neo-lang-switcher::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.neo-lang-switcher:hover::before {
  left: 100%;
}

.neo-lang-menu {
  background: rgba(20, 20, 20, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(242, 169, 0, 0.2);
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(242, 169, 0, 0.1);
}

.neo-lang-menu-header {
  background: linear-gradient(135deg, rgba(242, 169, 0, 0.1) 0%, rgba(242, 169, 0, 0.05) 100%);
}

.neo-lang-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.neo-lang-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(242, 169, 0, 0.1), transparent);
  transition: left 0.3s;
}

.neo-lang-item:hover::before {
  left: 100%;
}

.neo-lang-active {
  background: rgba(242, 169, 0, 0.1);
  border-left: 2px solid var(--pubg-orange);
}

.glow-effect {
  background: radial-gradient(circle, rgba(242, 169, 0, 0.2) 0%, transparent 70%);
  filter: blur(8px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.flag-container:hover .glow-effect {
  opacity: 1;
}
