import React from "react";
import { Metadata } from "next";
import <PERSON> from "@/components/Hero";
import FeaturedProducts from "@/components/FeaturedProducts";
import StatsSection from "@/components/StatsSection";
import GameFeatures from "@/components/GameFeatures";
import WhyChooseUs from "@/components/WhyChooseUs";

// SEO metadata
export const metadata: Metadata = {
  title: "RNG-STORE - Premium PUBG Accounts & UC",
  description: "Get the best PUBG accounts with rare outfits and UC packages at competitive prices.",
  openGraph: {
    title: "RNG-STORE - Premium PUBG Gaming Store",
    description: "Premium PUBG accounts and UC packages",
    type: "website",
    images: [{ url: "/og-image.png", width: 1200, height: 630 }],
  },
};

// Static data
const featuredAccounts = [
  {
    id: "1",
    name: { en: "Conqueror Elite Account", ar: "حساب كونكرر النخبة" },
    description: { en: "Premium PUBG account with exclusive skins", ar: "حساب PUBG مميز مع سكنز حصرية" },
    price: { egp: 2500, usd: 80 },
    image: "/placeholder.svg",
    type: "account" as const,
  },
  {
    id: "2",
    name: { en: "Crown Master Account", ar: "حساب كراون ماستر" },
    description: { en: "High-tier account with legendary skins", ar: "حساب عالي المستوى مع سكنز أسطورية" },
    price: { egp: 1800, usd: 60 },
    image: "/placeholder.svg",
    type: "account" as const,
  },
  {
    id: "3",
    name: { en: "Ace Collector Account", ar: "حساب آيس كوليكتور" },
    description: { en: "Ace tier account with rare items", ar: "حساب آيس مع عناصر نادرة" },
    price: { egp: 1200, usd: 40 },
    image: "/placeholder.svg",
    type: "account" as const,
  },
];

const statsData = [
  { value: "15K+", label: { en: "Happy Customers", ar: "عميل سعيد" }, icon: "users" },
  { value: "800+", label: { en: "Premium Accounts", ar: "حساب مميز" }, icon: "award" },
  { value: "24/7", label: { en: "Customer Support", ar: "دعم العملاء" }, icon: "clock" },
  { value: "100%", label: { en: "Secure Transactions", ar: "معاملات آمنة" }, icon: "shield" },
];

const gameFeatures = [
  {
    icon: "star",
    title: { en: "Premium Accounts", ar: "حسابات مميزة" },
    description: { en: "High-tier accounts with rare skins", ar: "حسابات عالية المستوى مع سكنز نادرة" },
  },
  {
    icon: "award",
    title: { en: "UC Packages", ar: "حزم UC" },
    description: { en: "Best prices for UC packages", ar: "أفضل أسعار لحزم UC" },
  },
  {
    icon: "clock",
    title: { en: "24/7 Support", ar: "دعم 24/7" },
    description: { en: "Always here to help", ar: "دائما هنا للمساعدة" },
  },
];

const whyChooseUsFeatures = [
  {
    icon: "shield",
    title: { en: "100% Secure", ar: "آمن 100%" },
    description: { en: "All transactions are encrypted and secure", ar: "جميع المعاملات مشفرة وآمنة" },
  },
  {
    icon: "award",
    title: { en: "Premium Quality", ar: "جودة مميزة" },
    description: { en: "Hand-picked accounts with verified rare items", ar: "حسابات مختارة بعناية مع عناصر نادرة" },
  },
  {
    icon: "clock",
    title: { en: "24/7 Support", ar: "دعم 24/7" },
    description: { en: "Always here to help you", ar: "دائما هنا لمساعدتك" },
  },
];

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Hero
        title="Elite PUBG Gaming Store"
        subtitle="Discover premium gaming accounts, exclusive UC packages, and powerful game enhancements. Level up your PUBG experience with our verified products."
        ctaText="Explore Products"
        ctaLink="/accounts"
        secondaryCtaText="View UC Packages"
        secondaryCtaLink="/uc"
        backgroundImage="https://i.ibb.co/N2Jb4PXP/cropped-image.jpg"
      />

      {/* Stats Section */}
      <StatsSection stats={statsData} />

      {/* Featured Accounts */}
      <FeaturedProducts
        title={{ en: "Featured Accounts", ar: "الحسابات المميزة" }}
        products={featuredAccounts}
        viewAllLink="/accounts"
        viewAllText={{ en: "View All Accounts", ar: "عرض جميع الحسابات" }}
      />

      {/* Game Features */}
      <GameFeatures features={gameFeatures} />

      {/* Why Choose Us */}
      <WhyChooseUs features={whyChooseUsFeatures} />
    </div>
  );
}
