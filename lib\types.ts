import { z } from 'zod';

// User roles and permissions
export enum UserRole {
  CUSTOMER = 'customer',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

export enum ProductType {
  HACK = 'hack',
  UC = 'uc',
  ACCOUNT = 'account'
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

// Localized content schema
export const LocalizedContentSchema = z.object({
  en: z.string(),
  ar: z.string()
});

// Price schema
export const PriceSchema = z.object({
  egp: z.number().positive(),
  usd: z.number().positive()
});

// Product schemas
export const BaseProductSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(ProductType),
  name: LocalizedContentSchema,
  description: LocalizedContentSchema,
  price: PriceSchema,
  images: z.array(z.string()).default([]),
  category: LocalizedContentSchema.optional(),
  tags: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.string(),
  updatedBy: z.string(),
  stockCount: z.number().int().min(0).default(0),
  lowStockThreshold: z.number().int().min(0).default(5),
  totalSold: z.number().int().min(0).default(0),
  rating: z.number().min(0).max(5).default(0),
  reviewCount: z.number().int().min(0).default(0)
});

export const HackProductSchema = BaseProductSchema.extend({
  type: z.literal(ProductType.HACK),
  specs: z.record(LocalizedContentSchema).optional(),
  features: z.object({
    en: z.array(z.string()),
    ar: z.array(z.string())
  }).optional(),
  compatibility: LocalizedContentSchema.optional(),
  detectionStatus: LocalizedContentSchema.optional(),
  downloadUrl: z.string().optional(),
  version: z.string().optional(),
  fileSize: z.string().optional()
});

export const UCProductSchema = BaseProductSchema.extend({
  type: z.literal(ProductType.UC),
  quantity: z.number().int().positive(),
  bonusPercentage: z.number().min(0).default(0),
  deliveryTime: LocalizedContentSchema.optional(),
  features: z.object({
    en: z.array(z.string()),
    ar: z.array(z.string())
  }).optional()
});

export const AccountProductSchema = BaseProductSchema.extend({
  type: z.literal(ProductType.ACCOUNT),
  specs: z.record(LocalizedContentSchema).optional(),
  features: z.object({
    en: z.array(z.string()),
    ar: z.array(z.string())
  }).optional(),
  gameType: LocalizedContentSchema.optional(),
  accountLevel: z.number().int().min(1).optional(),
  rank: LocalizedContentSchema.optional(),
  skins: z.array(LocalizedContentSchema).default([]),
  achievements: z.array(LocalizedContentSchema).default([])
});

// Union type for all products
export const ProductSchema = z.discriminatedUnion('type', [
  HackProductSchema,
  UCProductSchema,
  AccountProductSchema
]);

// User schema
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  displayName: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  photoURL: z.string().optional(),
  role: z.nativeEnum(UserRole).default(UserRole.CUSTOMER),
  isActive: z.boolean().default(true),
  isEmailVerified: z.boolean().default(false),
  country: z.string().optional(),
  city: z.string().optional(),
  dateOfBirth: z.string().optional(),
  bio: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  lastLoginAt: z.string().optional(),
  totalOrders: z.number().int().min(0).default(0),
  totalSpent: z.number().min(0).default(0),
  loyaltyPoints: z.number().int().min(0).default(0)
});

// Order item schema
export const OrderItemSchema = z.object({
  productId: z.string(),
  productName: LocalizedContentSchema,
  productType: z.nativeEnum(ProductType),
  quantity: z.number().int().positive(),
  unitPrice: PriceSchema,
  totalPrice: PriceSchema,
  productImage: z.string().optional()
});

// Order schema
export const OrderSchema = z.object({
  id: z.string(),
  orderNumber: z.string(),
  customerId: z.string(),
  customerEmail: z.string(),
  customerName: z.string(),
  items: z.array(OrderItemSchema),
  subtotal: PriceSchema,
  tax: PriceSchema.optional(),
  discount: PriceSchema.optional(),
  total: PriceSchema,
  status: z.nativeEnum(OrderStatus),
  paymentStatus: z.nativeEnum(PaymentStatus),
  paymentMethod: z.string().optional(),
  paymentId: z.string().optional(),
  notes: z.string().optional(),
  deliveryInstructions: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  completedAt: z.string().optional(),
  cancelledAt: z.string().optional(),
  refundedAt: z.string().optional(),
  processedBy: z.string().optional()
});

// Analytics schemas
export const SalesAnalyticsSchema = z.object({
  date: z.string(),
  totalRevenue: PriceSchema,
  totalOrders: z.number().int().min(0),
  averageOrderValue: PriceSchema,
  newCustomers: z.number().int().min(0),
  returningCustomers: z.number().int().min(0)
});

export const ProductAnalyticsSchema = z.object({
  productId: z.string(),
  productName: LocalizedContentSchema,
  productType: z.nativeEnum(ProductType),
  totalSold: z.number().int().min(0),
  revenue: PriceSchema,
  views: z.number().int().min(0),
  conversionRate: z.number().min(0).max(100)
});

// Type exports
export type LocalizedContent = z.infer<typeof LocalizedContentSchema>;
export type Price = z.infer<typeof PriceSchema>;
export type Product = z.infer<typeof ProductSchema>;
export type HackProduct = z.infer<typeof HackProductSchema>;
export type UCProduct = z.infer<typeof UCProductSchema>;
export type AccountProduct = z.infer<typeof AccountProductSchema>;
export type User = z.infer<typeof UserSchema>;
export type OrderItem = z.infer<typeof OrderItemSchema>;
export type Order = z.infer<typeof OrderSchema>;
export type SalesAnalytics = z.infer<typeof SalesAnalyticsSchema>;
export type ProductAnalytics = z.infer<typeof ProductAnalyticsSchema>;

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Filter and search types
export interface ProductFilters {
  type?: ProductType;
  category?: string;
  priceRange?: {
    min: number;
    max: number;
    currency: 'egp' | 'usd';
  };
  isActive?: boolean;
  isFeatured?: boolean;
  inStock?: boolean;
  tags?: string[];
  search?: string;
}

export interface OrderFilters {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  customerId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
}

export interface UserFilters {
  role?: UserRole;
  isActive?: boolean;
  country?: string;
  registrationDateRange?: {
    start: string;
    end: string;
  };
  search?: string;
}

// Dashboard stats types
export interface DashboardStats {
  totalProducts: number;
  totalUsers: number;
  totalOrders: number;
  totalRevenue: Price;
  lowStockProducts: number;
  pendingOrders: number;
  newUsersToday: number;
  salesGrowth: number;
}
