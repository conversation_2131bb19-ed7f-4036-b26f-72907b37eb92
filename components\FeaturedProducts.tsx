"use client"

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import ProductCard from "@/components/ProductCard";

interface Product {
  id: string;
  name: { en: string; ar: string };
  description: { en: string; ar: string };
  price: { egp: number; usd: number };
  image: string;
  category?: { en: string; ar: string };
  quantity?: number;
  type: "account" | "uc" | "hack";
}

interface FeaturedProductsProps {
  title: { en: string; ar: string };
  products: Product[];
  viewAllLink: string;
  viewAllText: { en: string; ar: string };
  className?: string;
}

// Animation variants
const fadeInLeft = {
  hidden: { opacity: 0, x: -50 },
  visible: { opacity: 1, x: 0 },
};

const fadeInRight = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const popIn = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: { 
    scale: 1, 
    opacity: 1,
    transition: {
      type: "spring" as const,
      stiffness: 260,
      damping: 20
    }
  },
};

const FeaturedProducts = ({ 
  title, 
  products, 
  viewAllLink, 
  viewAllText, 
  className = "" 
}: FeaturedProductsProps) => {
  const { language } = useLanguage();

  return (
    <motion.section
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={staggerContainer}
      className={`py-16 relative z-10 ${className}`}
    >
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <motion.h2 
            variants={fadeInLeft}
            className="text-3xl font-bold text-white"
          >
            {title[language]}
          </motion.h2>
          <motion.div variants={fadeInRight}>
            <Button 
              variant="outline" 
              className="text-white border-pubg-orange hover:bg-pubg-orange hover:text-pubg-dark"
              asChild
            >
              <Link href={viewAllLink}>
                {viewAllText[language]}
              </Link>
            </Button>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product, index) => (
            <motion.div
              key={product.id || index}
              variants={popIn}
              className="h-full"
            >
              <ProductCard {...product} />
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

export default FeaturedProducts; 