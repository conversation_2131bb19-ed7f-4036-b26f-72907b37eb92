// Performance monitoring and reporting
export function reportWebVitals(metric: any) {
  if (process.env.NODE_ENV === 'production') {
    // Log metrics in production
    console.log(metric)
    
    // You can send to analytics service here
    // Example: analytics.track('Web Vitals', metric)
  }
}

// Preload critical resources
export function preloadCriticalResources() {
  if (typeof window !== 'undefined') {
    // Preload critical images
    const criticalImages = [
      '/placeholder-logo.png',
      '/placeholder.svg'
    ]
    
    criticalImages.forEach(src => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = src
      document.head.appendChild(link)
    })
  }
}

// Optimize third-party scripts
export function loadThirdPartyScript(src: string, async = true) {
  if (typeof window !== 'undefined') {
    const script = document.createElement('script')
    script.src = src
    script.async = async
    document.head.appendChild(script)
  }
} 