"use client";

import { useState, useEffect, lazy, Suspense } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { LogIn, Mail, Lock, UserPlus, AlertTriangle, User } from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";

// Lazy load the background component
const BackgroundBoxesDemo = lazy(() => 
  import("@/components/ui/background-boxes-demo").then(mod => ({ 
    default: mod.BackgroundBoxesDemo 
  }))
);

// Enhanced animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: "beforeChildren",
      staggerChildren: 0.1,
      duration: 0.6,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      when: "afterChildren",
      staggerChildren: 0.05,
      staggerDirection: -1,
      duration: 0.4,
    },
  },
};

const cardVariants = {
  hidden: { opacity: 0, y: 50, scale: 0.95 },
  visible: {
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: { 
      type: "spring" as const, 
      damping: 15, 
      stiffness: 200, 
      duration: 0.8 
    }
  },
  exit: { 
    opacity: 0, 
    y: -30, 
    scale: 0.9,
    transition: { duration: 0.3 } 
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring" as const,
      stiffness: 300,
      damping: 24,
    },
  },
  exit: { opacity: 0, y: 10, transition: { duration: 0.2 } },
};

const buttonVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring" as const,
      stiffness: 400,
      damping: 20,
    },
  },
  hover: {
    scale: 1.05,
    boxShadow: "0px 0px 15px rgba(242, 169, 0, 0.5)",
    transition: { duration: 0.2 },
  },
  tap: { scale: 0.98 },
  exit: { opacity: 0, scale: 0.9, transition: { duration: 0.2 } },
};

const inputIconVariants = {
  hidden: { opacity: 0, x: -10 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { 
      type: "spring" as const, 
      stiffness: 300, 
      damping: 25 
    }
  },
  focusIn: { 
    scale: 1.2, 
    color: "#F2A900", 
    transition: { duration: 0.2 } 
  },
  focusOut: { 
    scale: 1, 
    color: "currentColor", 
    transition: { duration: 0.2 } 
  }
};

type AuthFormValues = {
  email: string;
  password: string;
  displayName?: string;
  confirmPassword?: string;
};

export default function AuthPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [signupError, setSignupError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("login");
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();
  const { signIn, signUp } = useAuth();

  // Add state to control background rendering
  const [shouldRenderBackground, setShouldRenderBackground] = useState(false);
  
  const loginForm = useForm<AuthFormValues>({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const registerForm = useForm<AuthFormValues>({
    defaultValues: {
      email: "",
      displayName: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Add background animation effect
  useEffect(() => {
    document.body.classList.add('auth-page-background');
    
    // Delay background rendering to prioritize UI first
    const timer = setTimeout(() => {
      setShouldRenderBackground(true);
    }, 300);
    
    return () => {
      clearTimeout(timer);
      document.body.classList.remove('auth-page-background');
    };
  }, []);

  const handleLogin = async (data: AuthFormValues) => {
    try {
      setIsLoading(true);
      setLoginError(null);
      await signIn(data.email, data.password);
      toast({
        title: "Success",
        description: "Logged in successfully!",
        variant: "default",
      });
      router.push("/");
    } catch (error: any) {
      console.error("Login error:", error);
      setLoginError("Failed to login. Please check your credentials.");
      toast({
        title: "Login Error",
        description: "Failed to login. Please check your credentials.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (data: AuthFormValues) => {
    if (data.password !== data.confirmPassword) {
      setSignupError("Passwords do not match");
      toast({
        title: "Registration Error",
        description: "Passwords do not match",
        variant: "destructive",
      });
      return;
    }

    if (!data.displayName) {
      setSignupError("Display name is required");
      toast({
        title: "Registration Error",
        description: "Display name is required",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      setSignupError(null);

      // Create user account with Supabase
      await signUp(data.email, data.password, data.displayName);

      toast({
        title: "Success",
        description: "Account created successfully! Please check your email to verify your account.",
        variant: "default",
      });

      // Don't redirect immediately, let user verify email first
      setActiveTab("login");
    } catch (error: any) {
      console.error("Signup error:", error);
      setSignupError("Failed to create account. Please try again.");
      toast({
        title: "Registration Error",
        description: "Failed to create account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div 
      className="min-h-screen w-full flex items-center justify-center pt-16 md:pt-24 pb-12 md:pb-16 px-4 relative overflow-hidden hardware-accelerated"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Background boxes fill the entire screen, but only render when needed */}
      {shouldRenderBackground && (
        <div className="absolute inset-0 z-0">
          <Suspense fallback={null}>
            <BackgroundBoxesDemo />
          </Suspense>
        </div>
      )}
      
      <div className="w-full max-w-md z-10 relative">
        {/* Site logo/name at the top */}
        <motion.div 
          className="text-center mb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h1 className="text-3xl font-bold text-white mb-2">
            RNG <span className="text-pubg-orange">STORE</span>
          </h1>
          <p className="text-muted-foreground">Welcome to RNG Store</p>
        </motion.div>

        <Tabs
          defaultValue="login"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <TabsList className="grid grid-cols-2 mb-6 md:mb-8">
              <TabsTrigger value="login" className="text-base relative overflow-hidden group">
                Login
                <motion.div 
                  className="absolute bottom-0 left-0 right-0 h-[2px] bg-pubg-orange"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: activeTab === "login" ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              </TabsTrigger>
              <TabsTrigger value="register" className="text-base relative overflow-hidden group">
                Sign Up
                <motion.div 
                  className="absolute bottom-0 left-0 right-0 h-[2px] bg-pubg-orange"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: activeTab === "register" ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              </TabsTrigger>
            </TabsList>
          </motion.div>

          <AnimatePresence mode="wait">
            {activeTab === "login" && (
              <TabsContent value="login" className="mt-0">
                <motion.div
                  key="login-card"
                  variants={cardVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="backdrop-blur-sm"
                >
                  <Card className="glass-card border-border relative">
                    {/* Subtle card glow effect */}
                    <motion.div 
                      className="absolute inset-0 bg-gradient-to-tr from-pubg-orange/5 to-blue-500/5 rounded-xl opacity-0"
                      animate={{ opacity: [0.1, 0.3, 0.1] }}
                      transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                    />
                    
                    <CardHeader className="space-y-2">
                      <motion.div variants={itemVariants}>
                        <CardTitle className="text-xl text-white flex items-center">
                          <LogIn className="mr-2 h-5 w-5 text-pubg-orange" />
                          Login
                        </CardTitle>
                      </motion.div>
                      <motion.div variants={itemVariants}>
                <CardDescription>
                          Access your RNG Store account
                </CardDescription>
                      </motion.div>
              </CardHeader>
                    
                    <CardContent>
                      <AnimatePresence>
                        {loginError && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Alert variant="destructive" className="mb-4 bg-red-500/20 border-red-600">
                              <AlertTriangle className="h-4 w-4" />
                              <AlertTitle>Login Error</AlertTitle>
                              <AlertDescription>
                                {loginError}
                              </AlertDescription>
                            </Alert>
                          </motion.div>
                        )}
                      </AnimatePresence>
                      
                      <Form {...loginForm}>
                        <form
                          onSubmit={loginForm.handleSubmit(handleLogin)}
                          className="space-y-4"
                        >
                          <motion.div variants={itemVariants}>
                            <FormField
                              control={loginForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">Email</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "login-email" ? "focusIn" : "focusOut"}
                                      >
                                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                  <Input
                                        placeholder="Enter your email"
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("login-email")}
                                        onBlur={() => setFocusedField(null)}
                  />
                </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div variants={itemVariants}>
                            <FormField
                              control={loginForm.control}
                              name="password"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">Password</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "login-password" ? "focusIn" : "focusOut"}
                                      >
                                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                  <Input
                    type="password"
                                        placeholder="Enter your password"
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("login-password")}
                                        onBlur={() => setFocusedField(null)}
                  />
                </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                                                     <motion.div variants={itemVariants}>
                             <motion.div
                               variants={buttonVariants}
                               whileHover="hover"
                               whileTap="tap"
                             >
                               <Button
                                 type="submit"
                                 className="w-full bg-pubg-orange hover:bg-pubg-orange/90 text-pubg-black font-bold form-button-glowing"
                                 disabled={isLoading}
                               >
                                 <motion.span className="relative flex items-center justify-center">
                                   {isLoading ? (
                                     <motion.div
                                       initial={{ opacity: 0 }}
                                       animate={{ opacity: 1 }}
                                       className="flex items-center"
                                     >
                                       <motion.div 
                                         className="h-5 w-5 mr-2 rounded-full border-2 border-transparent border-t-pubg-dark"
                                         animate={{ rotate: 360 }}
                                         transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                       />
                                       Logging in...
                                     </motion.div>
                                   ) : (
                                     <>
                                       <LogIn className="mr-2 h-4 w-4" />
                                       Login
                                     </>
                                   )}
                                 </motion.span>
                               </Button>
                             </motion.div>
                           </motion.div>
                        </form>
                      </Form>
              </CardContent>
                    
              <CardFooter>
                      <motion.div 
                        variants={itemVariants}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Button
                          variant="link"
                          className="text-muted-foreground hover:text-pubg-orange transition-colors duration-300"
                          onClick={() => setActiveTab("register")}
                        >
                          Don't have an account? Sign up
                        </Button>
                      </motion.div>
              </CardFooter>
            </Card>
                </motion.div>
        </TabsContent>
            )}

            {activeTab === "register" && (
              <TabsContent value="register" className="mt-0">
                <motion.div
                  key="register-card"
                  variants={cardVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="backdrop-blur-sm"
                >
                  <Card className="glass-card border-border relative">
                    {/* Subtle card glow effect */}
                    <motion.div 
                      className="absolute inset-0 bg-gradient-to-tr from-blue-500/5 to-pubg-orange/5 rounded-xl opacity-0"
                      animate={{ opacity: [0.1, 0.3, 0.1] }}
                      transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                    />
                    
                    <CardHeader className="space-y-2">
                      <motion.div variants={itemVariants}>
                        <CardTitle className="text-xl text-white flex items-center">
                          <UserPlus className="mr-2 h-5 w-5 text-pubg-orange" />
                          Sign Up
                        </CardTitle>
                      </motion.div>
                      <motion.div variants={itemVariants}>
                <CardDescription>
                          Create your RNG Store account
                </CardDescription>
                      </motion.div>
              </CardHeader>
                    
                    <CardContent>
                      <AnimatePresence>
                        {signupError && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Alert variant="destructive" className="mb-4 bg-red-500/20 border-red-600">
                              <AlertTriangle className="h-4 w-4" />
                              <AlertTitle>Registration Error</AlertTitle>
                              <AlertDescription>
                                {signupError}
                              </AlertDescription>
                            </Alert>
                          </motion.div>
                        )}
                      </AnimatePresence>
                      
                      <Form {...registerForm}>
                        <form
                          onSubmit={registerForm.handleSubmit(handleSignup)}
                          className="space-y-4"
                        >
                          <motion.div variants={itemVariants}>
                            <FormField
                              control={registerForm.control}
                              name="displayName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">Display Name</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "register-displayName" ? "focusIn" : "focusOut"}
                                      >
                                        <User className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                                      <Input
                                        placeholder="Enter your display name"
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("register-displayName")}
                                        onBlur={() => setFocusedField(null)}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div variants={itemVariants}>
                            <FormField
                              control={registerForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">Email</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "register-email" ? "focusIn" : "focusOut"}
                                      >
                                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                  <Input
                                        placeholder="Enter your email"
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("register-email")}
                                        onBlur={() => setFocusedField(null)}
                  />
                </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div variants={itemVariants}>
                            <FormField
                              control={registerForm.control}
                              name="password"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">Password</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "register-password" ? "focusIn" : "focusOut"}
                                      >
                                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                  <Input
                    type="password"
                                        placeholder="Enter your password"
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("register-password")}
                                        onBlur={() => setFocusedField(null)}
                  />
                </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div variants={itemVariants}>
                            <FormField
                              control={registerForm.control}
                              name="confirmPassword"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">Confirm Password</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "register-confirm" ? "focusIn" : "focusOut"}
                                      >
                                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                  <Input
                    type="password"
                                        placeholder="Confirm your password"
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("register-confirm")}
                                        onBlur={() => setFocusedField(null)}
                  />
                </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                                                     <motion.div variants={itemVariants}>
                             <motion.div
                               variants={buttonVariants}
                               whileHover="hover"
                               whileTap="tap"
                             >
                               <Button
                                 type="submit"
                                 className="w-full bg-pubg-orange hover:bg-pubg-orange/90 text-pubg-black font-bold form-button-glowing"
                                 disabled={isLoading}
                               >
                                 <motion.span className="relative flex items-center justify-center">
                                   {isLoading ? (
                                     <motion.div
                                       initial={{ opacity: 0 }}
                                       animate={{ opacity: 1 }}
                                       className="flex items-center"
                                     >
                                       <motion.div 
                                         className="h-5 w-5 mr-2 rounded-full border-2 border-transparent border-t-pubg-dark"
                                         animate={{ rotate: 360 }}
                                         transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                       />
                                       Creating account...
                                     </motion.div>
                                   ) : (
                                     <>
                                       <UserPlus className="mr-2 h-4 w-4" />
                                       Sign Up
                                     </>
                                   )}
                                 </motion.span>
                               </Button>
                             </motion.div>
                           </motion.div>
                        </form>
                      </Form>
              </CardContent>
                    
              <CardFooter>
                      <motion.div 
                        variants={itemVariants}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Button
                          variant="link"
                          className="text-muted-foreground hover:text-pubg-orange transition-colors duration-300"
                          onClick={() => setActiveTab("login")}
                        >
                          Already have an account? Login
                        </Button>
                      </motion.div>
              </CardFooter>
            </Card>
                </motion.div>
        </TabsContent>
            )}
          </AnimatePresence>
      </Tabs>
    </div>
    </motion.div>
  );
} 