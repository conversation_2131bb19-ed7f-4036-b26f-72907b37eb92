"use client";

import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { LogIn, Mail, Lock, UserPlus, AlertTriangle, User, Eye, EyeOff } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, <PERSON><PERSON>Tit<PERSON>, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";

// Form validation schemas
const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

const signupSchema = z.object({
  displayName: z.string().min(2, "Display name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(6, "Password must be at least 6 characters"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type SignupFormValues = z.infer<typeof signupSchema>;

export default function AuthPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("login");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const router = useRouter();
  const { toast } = useToast();
  const { signIn, signUp } = useAuth();
  // Form configurations with validation
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const signupForm = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      email: "",
      displayName: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Optimized handlers with useCallback to prevent unnecessary re-renders
  const handleLogin = useCallback(async (data: LoginFormValues) => {
    try {
      setIsLoading(true);
      setError(null);
      await signIn(data.email, data.password);
      toast({
        title: "Welcome back!",
        description: "You've been logged in successfully.",
      });
      router.push("/");
    } catch (error: any) {
      const errorMessage = error.message || "Failed to login. Please check your credentials.";
      setError(errorMessage);
      toast({
        title: "Login Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [signIn, router, toast]);

  const handleSignup = useCallback(async (data: SignupFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      await signUp(data.email, data.password, data.displayName);

      toast({
        title: "Account Created!",
        description: "Please check your email to verify your account before signing in.",
      });

      // Switch to login tab and reset signup form
      setActiveTab("login");
      signupForm.reset();
    } catch (error: any) {
      const errorMessage = error.message || "Failed to create account. Please try again.";
      setError(errorMessage);
      toast({
        title: "Registration Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [signUp, signupForm, toast]);

  // Memoized password toggle handlers
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const toggleConfirmPasswordVisibility = useCallback(() => {
    setShowConfirmPassword(prev => !prev);
  }, []);

  // Clear error when switching tabs
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);
    setError(null);
  }, []);

  return (
    <div className="min-h-screen w-full flex items-center justify-center pt-16 md:pt-24 pb-12 md:pb-16 px-4 relative">
      {/* Simplified background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(242,169,0,0.1),transparent_50%)]" />

      <div className="w-full max-w-md z-10 relative">
        {/* Optimized header */}
        <div className="text-center mb-8 animate-in fade-in slide-in-from-top-4 duration-700">
          <h1 className="text-4xl font-bold text-white mb-2 tracking-tight">
            RNG <span className="text-pubg-orange">STORE</span>
          </h1>
          <p className="text-slate-400 text-lg">Welcome to the ultimate gaming store</p>
        </div>

        <Tabs
          defaultValue="login"
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <div className="animate-in fade-in slide-in-from-top-2 duration-500 delay-200">
            <TabsList className="grid grid-cols-2 mb-8 h-12 bg-slate-800/50 backdrop-blur-sm border border-slate-700">
              <TabsTrigger
                value="login"
                className="text-base font-medium data-[state=active]:bg-pubg-orange data-[state=active]:text-slate-900 transition-all duration-200"
              >
                <LogIn className="mr-2 h-4 w-4" />
                Login
              </TabsTrigger>
              <TabsTrigger
                value="register"
                className="text-base font-medium data-[state=active]:bg-pubg-orange data-[state=active]:text-slate-900 transition-all duration-200"
              >
                <UserPlus className="mr-2 h-4 w-4" />
                Sign Up
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Login Form */}
          <TabsContent value="login" className="mt-0">
            <div className="animate-in fade-in slide-in-from-bottom-4 duration-500 delay-300">
              <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 shadow-2xl">
                <CardHeader className="space-y-2 pb-6">
                  <CardTitle className="text-2xl text-white flex items-center">
                    <LogIn className="mr-3 h-6 w-6 text-pubg-orange" />
                    Welcome Back
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    Sign in to access your RNG Store account
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Error Alert */}
                  {error && activeTab === "login" && (
                    <Alert variant="destructive" className="bg-red-500/10 border-red-500/50 animate-in fade-in slide-in-from-top-2 duration-300">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>Login Failed</AlertTitle>
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <Form {...loginForm}>
                    <form onSubmit={loginForm.handleSubmit(handleLogin)} className="space-y-5">
                      <FormField
                        control={loginForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-slate-200">Email Address</FormLabel>
                            <FormControl>
                              <div className="relative group">
                                <Mail className="absolute left-3 top-3 h-5 w-5 text-slate-400 group-focus-within:text-pubg-orange transition-colors duration-200" />
                                <Input
                                  type="email"
                                  placeholder="Enter your email address"
                                  className="pl-10 h-12 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-pubg-orange focus:ring-2 focus:ring-pubg-orange/20 transition-all duration-200"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={loginForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-slate-200">Password</FormLabel>
                            <FormControl>
                              <div className="relative group">
                                <Lock className="absolute left-3 top-3 h-5 w-5 text-slate-400 group-focus-within:text-pubg-orange transition-colors duration-200" />
                                <Input
                                  type={showPassword ? "text" : "password"}
                                  placeholder="Enter your password"
                                  className="pl-10 pr-12 h-12 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-pubg-orange focus:ring-2 focus:ring-pubg-orange/20 transition-all duration-200"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  onClick={togglePasswordVisibility}
                                  className="absolute right-3 top-3 h-6 w-6 text-slate-400 hover:text-pubg-orange transition-colors duration-200"
                                  aria-label={showPassword ? "Hide password" : "Show password"}
                                >
                                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="submit"
                        disabled={isLoading}
                        className="w-full h-12 bg-pubg-orange hover:bg-pubg-orange/90 text-slate-900 font-semibold transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isLoading ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-5 w-5 border-2 border-slate-900 border-t-transparent mr-2" />
                            Signing in...
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <LogIn className="mr-2 h-5 w-5" />
                            Sign In
                          </div>
                        )}
                      </Button>
                    </form>
                  </Form>
                </CardContent>

                <CardFooter className="pt-6">
                  <Button
                    variant="link"
                    className="w-full text-slate-400 hover:text-pubg-orange transition-colors duration-200"
                    onClick={() => handleTabChange("register")}
                  >
                    Don't have an account? <span className="font-semibold ml-1">Sign up</span>
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          {/* Signup Form */}
          <TabsContent value="register" className="mt-0">
            <div className="animate-in fade-in slide-in-from-bottom-4 duration-500 delay-300">
              <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 shadow-2xl">
                <CardHeader className="space-y-2 pb-6">
                  <CardTitle className="text-2xl text-white flex items-center">
                    <UserPlus className="mr-3 h-6 w-6 text-pubg-orange" />
                    Join RNG Store
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    Create your account to access premium gaming content
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-5">
                  {/* Error Alert */}
                  {error && activeTab === "register" && (
                    <Alert variant="destructive" className="bg-red-500/10 border-red-500/50 animate-in fade-in slide-in-from-top-2 duration-300">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>Registration Failed</AlertTitle>
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <Form {...signupForm}>
                    <form onSubmit={signupForm.handleSubmit(handleSignup)} className="space-y-4">
                      <FormField
                        control={signupForm.control}
                        name="displayName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-slate-200">Display Name</FormLabel>
                            <FormControl>
                              <div className="relative group">
                                <User className="absolute left-3 top-3 h-5 w-5 text-slate-400 group-focus-within:text-pubg-orange transition-colors duration-200" />
                                <Input
                                  placeholder="Choose your display name"
                                  className="pl-10 h-12 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-pubg-orange focus:ring-2 focus:ring-pubg-orange/20 transition-all duration-200"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-slate-200">Email Address</FormLabel>
                            <FormControl>
                              <div className="relative group">
                                <Mail className="absolute left-3 top-3 h-5 w-5 text-slate-400 group-focus-within:text-pubg-orange transition-colors duration-200" />
                                <Input
                                  type="email"
                                  placeholder="Enter your email address"
                                  className="pl-10 h-12 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-pubg-orange focus:ring-2 focus:ring-pubg-orange/20 transition-all duration-200"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-slate-200">Password</FormLabel>
                            <FormControl>
                              <div className="relative group">
                                <Lock className="absolute left-3 top-3 h-5 w-5 text-slate-400 group-focus-within:text-pubg-orange transition-colors duration-200" />
                                <Input
                                  type={showPassword ? "text" : "password"}
                                  placeholder="Create a strong password"
                                  className="pl-10 pr-12 h-12 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-pubg-orange focus:ring-2 focus:ring-pubg-orange/20 transition-all duration-200"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  onClick={togglePasswordVisibility}
                                  className="absolute right-3 top-3 h-6 w-6 text-slate-400 hover:text-pubg-orange transition-colors duration-200"
                                  aria-label={showPassword ? "Hide password" : "Show password"}
                                >
                                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={signupForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-slate-200">Confirm Password</FormLabel>
                            <FormControl>
                              <div className="relative group">
                                <Lock className="absolute left-3 top-3 h-5 w-5 text-slate-400 group-focus-within:text-pubg-orange transition-colors duration-200" />
                                <Input
                                  type={showConfirmPassword ? "text" : "password"}
                                  placeholder="Confirm your password"
                                  className="pl-10 pr-12 h-12 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-pubg-orange focus:ring-2 focus:ring-pubg-orange/20 transition-all duration-200"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  onClick={toggleConfirmPasswordVisibility}
                                  className="absolute right-3 top-3 h-6 w-6 text-slate-400 hover:text-pubg-orange transition-colors duration-200"
                                  aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                                >
                                  {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />

                                                 <Button
                        type="submit"
                        disabled={isLoading}
                        className="w-full h-12 bg-pubg-orange hover:bg-pubg-orange/90 text-slate-900 font-semibold transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isLoading ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-5 w-5 border-2 border-slate-900 border-t-transparent mr-2" />
                            Creating account...
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <UserPlus className="mr-2 h-5 w-5" />
                            Create Account
                          </div>
                        )}
                      </Button>
                    </form>
                  </Form>
                </CardContent>
                    
              <CardFooter>
                      <motion.div 
                        variants={itemVariants}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Button
                          variant="link"
                          className="text-muted-foreground hover:text-pubg-orange transition-colors duration-300"
                          onClick={() => setActiveTab("login")}
                        >
                          Already have an account? Login
                        </Button>
                      </motion.div>
              </CardFooter>
            </Card>
                </motion.div>
        </TabsContent>
            )}
          </AnimatePresence>
      </Tabs>
    </div>
    </motion.div>
  );
} 