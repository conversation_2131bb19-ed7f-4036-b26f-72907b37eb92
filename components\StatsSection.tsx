"use client"

import React from "react";
import { motion } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import * as LucideIcons from "lucide-react";

interface StatItem {
  value: string;
  label: { en: string; ar: string };
  icon: string;
}

interface StatsSectionProps {
  stats: StatItem[];
  title?: { en: string; ar: string };
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const popIn = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: { 
    scale: 1, 
    opacity: 1,
    transition: {
      type: "spring" as const,
      stiffness: 260,
      damping: 20
    }
  },
};

const StatsSection = ({ stats, title }: StatsSectionProps) => {
  const { language } = useLanguage();

  return (
    <motion.section
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={staggerContainer}
      className="py-12 md:py-16 relative z-10"
    >
      <div className="container mx-auto px-4">
        {title && (
          <motion.h2 
            variants={fadeInUp}
            className="text-2xl md:text-3xl font-bold text-white text-center mb-10"
          >
            {title[language]}
          </motion.h2>
        )}
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-10">
          {stats.map((stat, index) => {
            // Dynamically get the icon component
            const IconComponent = (LucideIcons as any)[stat.icon.charAt(0).toUpperCase() + stat.icon.slice(1)];
            return (
              <motion.div
                key={index}
                variants={popIn}
                className="flex flex-col items-center text-center"
              >
                <div className="w-14 h-14 rounded-full bg-card/30 backdrop-blur-sm border border-border/50 flex items-center justify-center mb-4">
                  <IconComponent className="w-6 h-6 text-pubg-orange" />
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-pubg-orange mb-1">
                  {stat.value}
                </h3>
                <p className="text-sm md:text-base text-gray-300">
                  {stat.label[language]}
                </p>
              </motion.div>
            );
          })}
        </div>
      </div>
    </motion.section>
  );
};

export default StatsSection; 