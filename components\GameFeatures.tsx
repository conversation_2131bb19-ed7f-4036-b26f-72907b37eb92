"use client"

import React from "react";
import { motion } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import * as LucideIcons from "lucide-react";

interface Feature {
  icon: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
}

interface GameFeaturesProps {
  features: Feature[];
  title?: { en: string; ar: string };
  subtitle?: { en: string; ar: string };
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const fadeInLeft = {
  hidden: { opacity: 0, x: -50 },
  visible: { opacity: 1, x: 0 },
};

const fadeInRight = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const GameFeatures = ({ features, title, subtitle }: GameFeaturesProps) => {
  const { language } = useLanguage();

  const defaultTitle = { en: "Game Features", ar: "ميزات اللعبة" };
  const defaultSubtitle = { 
    en: "Discover the amazing features that make our accounts and tools stand out", 
    ar: "اكتشف الميزات المذهلة التي تجعل حساباتنا وأدواتنا متميزة" 
  };

  const PlayIcon = LucideIcons.Play;

  return (
    <section className="py-20 px-4 bg-card/30 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/placeholder.svg')] bg-cover bg-center opacity-10"></div>
      <div className="container mx-auto relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={fadeInUp}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold text-white mb-4">
            {(title || defaultTitle)[language]}
          </h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {(subtitle || defaultSubtitle)[language]}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeInLeft}
          >
            <div className="relative rounded-xl overflow-hidden">
              <div className="aspect-video relative">
                <div className="absolute inset-0 bg-gradient-to-r from-pubg-orange/30 to-pubg-blue/30 mix-blend-overlay z-10"></div>
                <img 
                  src="/placeholder.svg" 
                  alt="PUBG Game Features"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="w-20 h-20 rounded-full bg-pubg-orange/80 flex items-center justify-center cursor-pointer shadow-lg"
                >
                  <PlayIcon className="h-10 w-10 text-white ml-1" />
                </motion.div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={staggerContainer}
          >
            <div className="space-y-6">
              {features.map((feature, index) => {
                const IconComponent = (LucideIcons as any)[feature.icon.charAt(0).toUpperCase() + feature.icon.slice(1)];
                return (
                  <motion.div 
                    key={index} 
                    variants={fadeInUp} 
                    className="flex gap-4"
                  >
                    <div className="shrink-0 w-12 h-12 bg-pubg-orange/20 rounded-full flex items-center justify-center">
                      <IconComponent className="text-pubg-orange w-6 h-6" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-white mb-1">
                        {feature.title[language]}
                      </h4>
                      <p className="text-muted-foreground">
                        {feature.description[language]}
                      </p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default GameFeatures; 