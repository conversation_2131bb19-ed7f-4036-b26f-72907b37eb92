import type { Metadata } from "next"
import ProductCard from "@/components/ProductCard"

export const metadata: Metadata = {
  title: "PUBG Accounts - RNG VIP",
  description: "Premium PUBG accounts for sale. High-tier accounts with exclusive skins and items.",
}

// Mock data for accounts
const accounts = [
  {
    id: "1",
    name: { en: "Conqueror Account #1", ar: "حساب كونكرر #1" },
    description: {
      en: "High-tier PUBG account with exclusive skins and rare items",
      ar: "حساب PUBG عالي المستوى مع سكنز حصرية وعناصر نادرة",
    },
    price: { egp: 1500, usd: 50 },
    image: "/placeholder.svg?height=200&width=300",
    type: "account" as const,
  },
  {
    id: "2",
    name: { en: "Crown Account #1", ar: "حساب كراون #1" },
    description: { en: "Premium account with multiple legendary skins", ar: "حساب مميز مع عدة سكنز أسطورية" },
    price: { egp: 900, usd: 30 },
    image: "/placeholder.svg?height=200&width=300",
    type: "account" as const,
  },
  {
    id: "3",
    name: { en: "Ace Account #1", ar: "حساب آيس #1" },
    description: { en: "Well-maintained account with good stats", ar: "حساب محافظ عليه مع إحصائيات جيدة" },
    price: { egp: 600, usd: 20 },
    image: "/placeholder.svg?height=200&width=300",
    type: "account" as const,
  },
  {
    id: "4",
    name: { en: "Diamond Account #1", ar: "حساب دايموند #1" },
    description: { en: "Solid account for competitive play", ar: "حساب قوي للعب التنافسي" },
    price: { egp: 450, usd: 15 },
    image: "/placeholder.svg?height=200&width=300",
    type: "account" as const,
  },
  {
    id: "5",
    name: { en: "Platinum Account #1", ar: "حساب بلاتينيوم #1" },
    description: { en: "Great starter account with decent items", ar: "حساب بداية رائع مع عناصر جيدة" },
    price: { egp: 300, usd: 10 },
    image: "/placeholder.svg?height=200&width=300",
    type: "account" as const,
  },
  {
    id: "6",
    name: { en: "Gold Account #1", ar: "حساب ذهبي #1" },
    description: { en: "Budget-friendly account for new players", ar: "حساب اقتصادي للاعبين الجدد" },
    price: { egp: 150, usd: 5 },
    image: "/placeholder.svg?height=200&width=300",
    type: "account" as const,
  },
]

export default function AccountsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-yellow-400 mb-4">PUBG Accounts</h1>
        <p className="text-gray-300 text-lg">Premium PUBG accounts with exclusive skins, high ranks, and rare items.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {accounts.map((account) => (
          <ProductCard key={account.id} {...account} />
        ))}
      </div>
    </div>
  )
}
