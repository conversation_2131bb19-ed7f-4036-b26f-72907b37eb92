import type { Metadata } from "next"
import { notFound } from "next/navigation"
import ImageGallery from "@/components/ImageGallery"
import ProductSpecs from "@/components/ProductSpecs"
import AddToCartButton from "@/components/AddToCartButton"
import { Shield, AlertTriangle } from "lucide-react"

// Mock data for hacks (all demo items so /hacks/1-6 resolve)
const hacksData = {
  "1": {
    id: "1",
    name: { en: "ESP Hack Pro", ar: "هاك ESP برو" },
    description: {
      en: "Advanced ESP hack with enemy detection through walls, item ESP, and vehicle tracking. Professional-grade tool for competitive advantage.",
      ar: "هاك ESP متقدم مع كشف الأعداء عبر الجدران، ESP للعناصر، وتتبع المركبات. أداة احترافية للحصول على ميزة تنافسية.",
    },
    price: { egp: 450, usd: 15 },
    category: { en: "Vision", ar: "الرؤية" },
    images: [
      "/placeholder.svg?height=400&width=600",
      "/placeholder.svg?height=400&width=600",
      "/placeholder.svg?height=400&width=600",
    ],
    specs: {
      compatibility: { en: "Android 7.0+", ar: "أندرويد 7.0+" },
      gameVersion: { en: "PUBG Mobile 2.9+", ar: "PUBG Mobile 2.9+" },
      updateFrequency: { en: "Weekly Updates", ar: "تحديثات أسبوعية" },
      support: { en: "24/7 Support", ar: "دعم 24/7" },
      detection: { en: "Undetected", ar: "غير قابل للكشف" },
      installation: { en: "Easy Setup", ar: "تثبيت سهل" },
    },
    features: {
      en: [
        "Enemy ESP through walls",
        "Item and loot ESP",
        "Vehicle location tracking",
        "Distance indicators",
        "Health and armor display",
        "Customizable ESP colors",
        "Anti-detection system",
        "Regular updates",
      ],
      ar: [
        "ESP للأعداء عبر الجدران",
        "ESP للعناصر والغنائم",
        "تتبع مواقع المركبات",
        "مؤشرات المسافة",
        "عرض الصحة والدروع",
        "ألوان ESP قابلة للتخصيص",
        "نظام مكافحة الكشف",
        "تحديثات منتظمة",
      ],
    },
    requirements: {
      en: [
        "Android device with root access",
        "PUBG Mobile installed",
        "Stable internet connection",
        "At least 2GB RAM",
        "Storage space: 100MB",
      ],
      ar: [
        "جهاز أندرويد مع صلاحيات الروت",
        "PUBG Mobile مثبت",
        "اتصال إنترنت مستقر",
        "ذاكرة وصول عشوائي 2 جيجا على الأقل",
        "مساحة تخزين: 100 ميجا",
      ],
    },
  },

  "2": {
    id: "2",
    name: { en: "Aimbot Elite", ar: "ايمبوت إليت" },
    description: {
      en: "Professional aiming assistance with customizable settings.",
      ar: "مساعدة تصويب احترافية مع إعدادات قابلة للتخصيص.",
    },
    price: { egp: 750, usd: 25 },
    category: { en: "Aiming", ar: "التصويب" },
    images: ["/placeholder.svg?height=400&width=600"],
    specs: {
      compatibility: { en: "Android 7.0+", ar: "أندرويد 7.0+" },
      detection: { en: "Undetected", ar: "غير قابل للكشف" },
    },
    features: {
      en: ["Auto-aim", "Recoil control", "Custom FOV", "Weekly updates"],
      ar: ["تصويب تلقائي", "تحكم بالارتداد", "مجال رؤية مخصص", "تحديثات أسبوعية"],
    },
    requirements: {
      en: ["Root device", "Stable internet", "PUBG 2.9+"],
      ar: ["جهاز بصلاحيات روت", "إنترنت مستقر", "PUBG 2.9+"],
    },
  },

  "3": {
    id: "3",
    name: { en: "Speed Hack", ar: "هاك السرعة" },
    description: {
      en: "Increase movement speed for tactical advantage.",
      ar: "زيادة سرعة الحركة للحصول على ميزة تكتيكية.",
    },
    price: { egp: 300, usd: 10 },
    category: { en: "Movement", ar: "الحركة" },
    images: ["/placeholder.svg?height=400&width=600"],
    specs: {
      compatibility: { en: "Android 7.0+", ar: "أندرويد 7.0+" },
      detection: { en: "Undetected", ar: "غير قابل للكشف" },
    },
    features: {
      en: ["Sprint boost", "Vehicle boost", "Safe-mode toggle"],
      ar: ["زيادة سرعة الجري", "زيادة سرعة المركبة", "وضع آمن"],
    },
    requirements: {
      en: ["Root device", "PUBG 2.9+", "100 MB storage"],
      ar: ["جهاز بصلاحيات روت", "PUBG 2.9+", "مساحة 100 ميجا"],
    },
  },

  "4": {
    id: "4",
    name: { en: "No-Recoil Hack", ar: "هاك بدون ارتداد" },
    description: {
      en: "Eliminate weapon recoil for perfect accuracy.",
      ar: "إزالة ارتداد السلاح للحصول على دقة مثالية.",
    },
    price: { egp: 600, usd: 20 },
    category: { en: "Weapon", ar: "السلاح" },
    images: ["/placeholder.svg?height=400&width=600"],
    specs: {
      compatibility: { en: "Android 7.0+", ar: "أندرويد 7.0+" },
      detection: { en: "Undetected", ar: "غير قابل للكشف" },
    },
    features: {
      en: ["Zero recoil", "Bullet spread fix", "ADS assist"],
      ar: ["صفر ارتداد", "إصلاح تشتت الرصاصة", "مساعدة عند التصويب"],
    },
    requirements: {
      en: ["Root device", "PUBG 2.9+"],
      ar: ["جهاز بصلاحيات روت", "PUBG 2.9+"],
    },
  },

  "5": {
    id: "5",
    name: { en: "Radar Hack", ar: "هاك الرادار" },
    description: {
      en: "See all players on minimap.",
      ar: "رؤية جميع اللاعبين على الخريطة المصغرة.",
    },
    price: { egp: 400, usd: 13 },
    category: { en: "Vision", ar: "الرؤية" },
    images: ["/placeholder.svg?height=400&width=600"],
    specs: {
      compatibility: { en: "Android 7.0+", ar: "أندرويد 7.0+" },
      detection: { en: "Undetected", ar: "غير قابل للكشف" },
    },
    features: {
      en: ["Real-time enemy dots", "Vehicle tracking", "Team filter"],
      ar: ["عرض الأعداء في الوقت الحقيقي", "تتبع المركبات", "تصفية الفريق"],
    },
    requirements: {
      en: ["No root required", "VPN recommended"],
      ar: ["لا يتطلب روت", "يوصى باستخدام VPN"],
    },
  },

  "6": {
    id: "6",
    name: { en: "Magic Bullet", ar: "الرصاصة السحرية" },
    description: {
      en: "Bullets automatically hit targets.",
      ar: "الرصاصات تصيب الأهداف تلقائياً.",
    },
    price: { egp: 900, usd: 30 },
    category: { en: "Weapon", ar: "السلاح" },
    images: ["/placeholder.svg?height=400&width=600"],
    specs: {
      compatibility: { en: "Android 7.0+", ar: "أندرويد 7.0+" },
      detection: { en: "Risky - use carefully", ar: "محفوف بالمخاطر - استخدم بحذر" },
    },
    features: {
      en: ["Auto-hit bullets", "Head-shot bias", "Distance compensation"],
      ar: ["رصاصات تصيب تلقائياً", "تحيّز لإصابة الرأس", "تعويض المسافة"],
    },
    requirements: {
      en: ["Root device", "Anti-ban VPN"],
      ar: ["جهاز بصلاحيات روت", "VPN ضد الحظر"],
    },
  },
} satisfies Record<string, any>

interface PageProps {
  params: { id: string }
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const hack = hacksData[params.id as keyof typeof hacksData]

  if (!hack) {
    return {
      title: "Hack Not Found - RNG VIP",
    }
  }

  return {
    title: `${hack.name.en} - RNG VIP`,
    description: hack.description.en,
  }
}

export default function HackDetailPage({ params }: PageProps) {
  const hack = hacksData[params.id as keyof typeof hacksData]

  if (!hack) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Warning Banner */}
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-8">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="text-red-400 mt-1" size={20} />
          <div>
            <h3 className="text-red-400 font-semibold mb-1">Important Notice</h3>
            <p className="text-gray-300 text-sm">
              Use of third-party tools may violate game terms of service. Use at your own risk. We recommend using these
              tools responsibly and understanding the potential consequences.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Gallery */}
        <div>
          <ImageGallery images={hack.images} productName={hack.name.en} />
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <div className="flex items-center space-x-4 mb-4">
              <h1 className="text-3xl font-bold text-yellow-400">{hack.name.en}</h1>
              <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                {hack.category.en}
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-gray-300 mb-4">{hack.name.ar}</h2>
            <p className="text-gray-300 leading-relaxed">{hack.description.en}</p>
            <p className="text-gray-400 leading-relaxed mt-2">{hack.description.ar}</p>
          </div>

          {/* Security Badge */}
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <Shield className="text-green-400" size={24} />
              <div>
                <h3 className="text-green-400 font-semibold">Security Features</h3>
                <p className="text-gray-300 text-sm">
                  Advanced anti-detection system with regular updates to maintain security.
                </p>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
            <h3 className="text-xl font-bold text-yellow-400 mb-4">Hack Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-white mb-2">English</h4>
                <ul className="space-y-1">
                  {hack.features.en.map((feature, index) => (
                    <li key={index} className="text-gray-300 text-sm flex items-start">
                      <span className="text-yellow-400 mr-2">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">العربية</h4>
                <ul className="space-y-1">
                  {hack.features.ar.map((feature, index) => (
                    <li key={index} className="text-gray-300 text-sm flex items-start">
                      <span className="text-yellow-400 mr-2">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Add to Cart */}
          <AddToCartButton productId={hack.id} productType="hack" price={hack.price} />
        </div>
      </div>

      {/* Specifications and Requirements */}
      <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ProductSpecs specs={hack.specs} />

        <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
          <h3 className="text-xl font-bold text-yellow-400 mb-4">System Requirements</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-white mb-2">English</h4>
              <ul className="space-y-2">
                {hack.requirements.en.map((req, index) => (
                  <li key={index} className="text-gray-300 text-sm flex items-start">
                    <span className="text-yellow-400 mr-2">•</span>
                    {req}
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-2">العربية</h4>
              <ul className="space-y-2">
                {hack.requirements.ar.map((req, index) => (
                  <li key={index} className="text-gray-300 text-sm flex items-start">
                    <span className="text-yellow-400 mr-2">•</span>
                    {req}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
