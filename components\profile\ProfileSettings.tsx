"use client"

import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { useAuth } from "@/contexts/AuthContext"
import { Bell, Shield, Globe, Lock, Trash2, Moon, Sun, Eye, EyeOff } from "lucide-react"

export default function ProfileSettings() {
  const { t, language, toggleLanguage } = useLanguage()
  const { signOut } = useAuth()
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: false,
      sms: false,
      orderUpdates: true,
      promotions: false,
      security: true,
    },
    privacy: {
      profileVisibility: "public",
      showOrderHistory: false,
      showGamingStats: true,
    },
    preferences: {
      theme: "dark",
      currency: "EGP",
    },
  })

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showPasswordChange, setShowPasswordChange] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordData, setPasswordData] = useState({
    current: "",
    new: "",
    confirm: "",
  })

  const handleSettingChange = (category: string, setting: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [setting]: value,
      },
    }))
  }

  const handlePasswordChange = async () => {
    if (passwordData.new !== passwordData.confirm) {
      alert(t("passwordsDontMatch"))
      return
    }
    
    if (passwordData.new.length < 6) {
      alert(t("passwordMinLength"))
      return
    }

    try {
      // TODO: Implement password change with Supabase
      console.log("Password change requested")
      setShowPasswordChange(false)
      setPasswordData({ current: "", new: "", confirm: "" })
      alert("Password changed successfully!")
    } catch (error) {
      console.error("Error changing password:", error)
      alert("Failed to change password. Please try again.")
    }
  }

  const handleDeleteAccount = async () => {
    if (showDeleteConfirm) {
      try {
        // TODO: Implement account deletion
        console.log("Account deletion requested")
        await signOut()
      } catch (error) {
        console.error("Error deleting account:", error)
      }
    } else {
      setShowDeleteConfirm(true)
    }
  }

  const notificationLabels = {
    email: t("emailNotifications"),
    push: t("pushNotifications"),
    sms: t("smsNotifications"),
    orderUpdates: t("orderUpdates"),
    promotions: t("promotions"),
    security: t("securityAlerts"),
  }

  const notificationDescriptions = {
    email: t("receiveEmailNotifications"),
    push: t("receivePushNotifications"),
    sms: t("receiveSmsNotifications"),
    orderUpdates: t("getOrderUpdates"),
    promotions: t("receivePromotions"),
    security: t("receiveSecurityAlerts"),
  }

  return (
    <div className={`space-y-6 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Notification Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Bell className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">{t("notificationSettings")}</h3>
        </div>

        <div className="space-y-4">
          {Object.entries(settings.notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div>
                <div className="text-white font-medium">
                  {notificationLabels[key as keyof typeof notificationLabels]}
                </div>
                <div className="text-gray-400 text-sm">
                  {notificationDescriptions[key as keyof typeof notificationDescriptions]}
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => handleSettingChange("notifications", key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Shield className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">{t("privacySettings")}</h3>
        </div>

        <div className="space-y-4">
          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">{t("profileVisibility")}</div>
              <select
                value={settings.privacy.profileVisibility}
                onChange={(e) => handleSettingChange("privacy", "profileVisibility", e.target.value)}
                className="bg-gray-600 border border-gray-500 rounded-md px-3 py-1 text-white text-sm"
              >
                <option value="public">{t("public")}</option>
                <option value="friends">{t("friendsOnly")}</option>
                <option value="private">{t("private")}</option>
              </select>
            </div>
            <div className="text-gray-400 text-sm">{t("controlProfileVisibility")}</div>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div>
              <div className="text-white font-medium">{t("showOrderHistory")}</div>
              <div className="text-gray-400 text-sm">{t("displayOrderHistory")}</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.privacy.showOrderHistory}
                onChange={(e) => handleSettingChange("privacy", "showOrderHistory", e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div>
              <div className="text-white font-medium">{t("showGamingStats")}</div>
              <div className="text-gray-400 text-sm">{t("displayGamingStats")}</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.privacy.showGamingStats}
                onChange={(e) => handleSettingChange("privacy", "showGamingStats", e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Preferences */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Globe className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">{t("preferences")}</h3>
        </div>

        <div className="space-y-4">
          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">{t("language")}</div>
              <button
                onClick={toggleLanguage}
                className="bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-md font-medium transition-colors"
              >
                {language === "en" ? "العربية" : "English"}
              </button>
            </div>
            <div className="text-gray-400 text-sm">{t("chooseLanguage")}</div>
          </div>

          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">{t("theme")}</div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleSettingChange("preferences", "theme", "dark")}
                  className={`p-2 rounded-md transition-colors ${
                    settings.preferences.theme === "dark"
                      ? "bg-yellow-500 text-black"
                      : "bg-gray-600 text-gray-300 hover:bg-gray-500"
                  }`}
                >
                  <Moon size={16} />
                </button>
                <button
                  onClick={() => handleSettingChange("preferences", "theme", "light")}
                  className={`p-2 rounded-md transition-colors ${
                    settings.preferences.theme === "light"
                      ? "bg-yellow-500 text-black"
                      : "bg-gray-600 text-gray-300 hover:bg-gray-500"
                  }`}
                >
                  <Sun size={16} />
                </button>
              </div>
            </div>
            <div className="text-gray-400 text-sm">{t("selectTheme")}</div>
          </div>

          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">{t("preferredCurrency")}</div>
              <select
                value={settings.preferences.currency}
                onChange={(e) => handleSettingChange("preferences", "currency", e.target.value)}
                className="bg-gray-600 border border-gray-500 rounded-md px-3 py-1 text-white text-sm"
              >
                <option value="EGP">EGP (Egyptian Pound)</option>
                <option value="USD">USD (US Dollar)</option>
                <option value="SAR">SAR (Saudi Riyal)</option>
                <option value="AED">AED (UAE Dirham)</option>
              </select>
            </div>
            <div className="text-gray-400 text-sm">{t("displayPricesInCurrency")}</div>
          </div>
        </div>
      </div>

      {/* Security Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Lock className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">{t("security")}</h3>
        </div>

        <div className="space-y-4">
          <button
            onClick={() => setShowPasswordChange(true)}
            className="w-full flex items-center justify-between p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
          >
            <div className="text-left">
              <div className="text-white font-medium">{t("changePassword")}</div>
              <div className="text-gray-400 text-sm">{t("updatePassword")}</div>
            </div>
            <Lock size={20} className="text-gray-400" />
          </button>

          <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
            <div>
              <div className="text-white font-medium">{t("twoFactorAuth")}</div>
              <div className="text-gray-400 text-sm">{t("enableTwoFactor")}</div>
            </div>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
              Enable
            </button>
          </div>

          <button className="w-full flex items-center justify-between p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
            <div className="text-left">
              <div className="text-white font-medium">{t("activeSessions")}</div>
              <div className="text-gray-400 text-sm">{t("manageActiveSessions")}</div>
            </div>
            <div className="text-gray-400">2 active</div>
          </button>
        </div>
      </div>

      {/* Danger Zone */}
      <div className="bg-red-900/20 rounded-lg p-6 border border-red-500/30">
        <div className="flex items-center space-x-3 mb-6">
          <Trash2 className="text-red-400" size={24} />
          <h3 className="text-xl font-bold text-red-400">{t("dangerZone")}</h3>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-red-900/30 rounded-lg border border-red-500/20">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">{t("deleteAccount")}</div>
                <div className="text-gray-400 text-sm">{t("deleteAccountWarning")}</div>
              </div>
              <button
                onClick={handleDeleteAccount}
                className={`px-4 py-2 rounded-md transition-colors ${
                  showDeleteConfirm
                    ? "bg-red-600 hover:bg-red-700 text-white"
                    : "bg-red-500/20 hover:bg-red-500/30 text-red-400 border border-red-500/30"
                }`}
              >
                {showDeleteConfirm ? "Confirm Delete" : "Delete Account"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Password Change Modal */}
      {showPasswordChange && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-white mb-4">{t("changePassword")}</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t("currentPassword")}
                </label>
                <div className="relative">
                  <input
                    type={showCurrentPassword ? "text" : "password"}
                    value={passwordData.current}
                    onChange={(e) => setPasswordData({ ...passwordData, current: e.target.value })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
                  >
                    {showCurrentPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t("newPassword")}
                </label>
                <div className="relative">
                  <input
                    type={showNewPassword ? "text" : "password"}
                    value={passwordData.new}
                    onChange={(e) => setPasswordData({ ...passwordData, new: e.target.value })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
                  >
                    {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t("confirmNewPassword")}
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    value={passwordData.confirm}
                    onChange={(e) => setPasswordData({ ...passwordData, confirm: e.target.value })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
                  >
                    {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={handlePasswordChange}
                className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-md font-medium transition-colors"
              >
                {t("save")}
              </button>
              <button
                onClick={() => {
                  setShowPasswordChange(false)
                  setPasswordData({ current: "", new: "", confirm: "" })
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                {t("cancel")}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
