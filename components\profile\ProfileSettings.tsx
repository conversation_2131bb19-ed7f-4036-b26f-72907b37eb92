"use client"

import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { Bell, Shield, Globe, Moon, Sun, AlertTriangle, Save, Lock } from "lucide-react"

export default function ProfileSettings() {
  const { t, language, toggleLanguage } = useLanguage()
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: false,
      sms: false,
      orderUpdates: true,
      promotions: false,
      security: true,
    },
    privacy: {
      profileVisibility: "public",
      showOrderHistory: false,
      showStats: true,
    },
    preferences: {
      theme: "dark",
      currency: "EGP",
      timezone: "Africa/Cairo",
    },
  })

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showPasswordChange, setShowPasswordChange] = useState(false)
  const [passwordData, setPasswordData] = useState({
    current: "",
    new: "",
    confirm: "",
  })

  const handleSettingChange = (category: string, setting: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [setting]: value,
      },
    }))
  }

  const handlePasswordChange = () => {
    // Password change logic here
    console.log("Password change requested")
    setShowPasswordChange(false)
    setPasswordData({ current: "", new: "", confirm: "" })
  }

  return (
    <div className="space-y-6">
      {/* Notification Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Bell className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">Notification Settings</h3>
        </div>

        <div className="space-y-4">
          {Object.entries(settings.notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div>
                <div className="text-white font-medium capitalize">
                  {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}
                </div>
                <div className="text-gray-400 text-sm">
                  {key === "email" && "Receive notifications via email"}
                  {key === "push" && "Browser push notifications"}
                  {key === "sms" && "SMS notifications for important updates"}
                  {key === "orderUpdates" && "Get notified about order status changes"}
                  {key === "promotions" && "Receive promotional offers and discounts"}
                  {key === "security" && "Security alerts and login notifications"}
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => handleSettingChange("notifications", key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Shield className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">Privacy Settings</h3>
        </div>

        <div className="space-y-4">
          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">Profile Visibility</div>
              <select
                value={settings.privacy.profileVisibility}
                onChange={(e) => handleSettingChange("privacy", "profileVisibility", e.target.value)}
                className="bg-gray-600 border border-gray-500 rounded-md px-3 py-1 text-white text-sm"
              >
                <option value="public">Public</option>
                <option value="friends">Friends Only</option>
                <option value="private">Private</option>
              </select>
            </div>
            <div className="text-gray-400 text-sm">Control who can see your profile information</div>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div>
              <div className="text-white font-medium">Show Order History</div>
              <div className="text-gray-400 text-sm">Allow others to see your purchase history</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.privacy.showOrderHistory}
                onChange={(e) => handleSettingChange("privacy", "showOrderHistory", e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div>
              <div className="text-white font-medium">Show Gaming Stats</div>
              <div className="text-gray-400 text-sm">Display your gaming statistics on profile</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.privacy.showStats}
                onChange={(e) => handleSettingChange("privacy", "showStats", e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Preferences */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Globe className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">Preferences</h3>
        </div>

        <div className="space-y-4">
          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">Language</div>
              <button
                onClick={toggleLanguage}
                className="bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-md font-medium transition-colors"
              >
                {language === "en" ? "العربية" : "English"}
              </button>
            </div>
            <div className="text-gray-400 text-sm">Choose your preferred language</div>
          </div>

          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">Theme</div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleSettingChange("preferences", "theme", "dark")}
                  className={`p-2 rounded-md transition-colors ${
                    settings.preferences.theme === "dark"
                      ? "bg-yellow-500 text-black"
                      : "bg-gray-600 text-gray-300 hover:bg-gray-500"
                  }`}
                >
                  <Moon size={16} />
                </button>
                <button
                  onClick={() => handleSettingChange("preferences", "theme", "light")}
                  className={`p-2 rounded-md transition-colors ${
                    settings.preferences.theme === "light"
                      ? "bg-yellow-500 text-black"
                      : "bg-gray-600 text-gray-300 hover:bg-gray-500"
                  }`}
                >
                  <Sun size={16} />
                </button>
              </div>
            </div>
            <div className="text-gray-400 text-sm">Choose between dark and light theme</div>
          </div>

          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-white font-medium">Preferred Currency</div>
              <select
                value={settings.preferences.currency}
                onChange={(e) => handleSettingChange("preferences", "currency", e.target.value)}
                className="bg-gray-600 border border-gray-500 rounded-md px-3 py-1 text-white text-sm"
              >
                <option value="EGP">EGP (Egyptian Pound)</option>
                <option value="USD">USD (US Dollar)</option>
                <option value="SAR">SAR (Saudi Riyal)</option>
                <option value="AED">AED (UAE Dirham)</option>
              </select>
            </div>
            <div className="text-gray-400 text-sm">Display prices in your preferred currency</div>
          </div>
        </div>
      </div>

      {/* Security Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center space-x-3 mb-6">
          <Lock className="text-yellow-400" size={24} />
          <h3 className="text-xl font-bold text-yellow-400">Security</h3>
        </div>

        <div className="space-y-4">
          <button
            onClick={() => setShowPasswordChange(true)}
            className="w-full flex items-center justify-between p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
          >
            <div className="text-left">
              <div className="text-white font-medium">Change Password</div>
              <div className="text-gray-400 text-sm">Update your account password</div>
            </div>
            <Lock size={20} className="text-gray-400" />
          </button>

          <div className="p-4 bg-gray-700 rounded-lg">
            <div className="text-white font-medium mb-2">Two-Factor Authentication</div>
            <div className="text-gray-400 text-sm mb-3">Add an extra layer of security to your account</div>
            <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md font-medium transition-colors">
              Enable 2FA
            </button>
          </div>

          <div className="p-4 bg-gray-700 rounded-lg">
            <div className="text-white font-medium mb-2">Active Sessions</div>
            <div className="text-gray-400 text-sm mb-3">Manage your active login sessions</div>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2 bg-gray-600 rounded">
                <div>
                  <div className="text-white text-sm">Current Session</div>
                  <div className="text-gray-400 text-xs">Chrome on Windows • Cairo, Egypt</div>
                </div>
                <span className="text-green-400 text-xs">Active</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Danger Zone */}
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <AlertTriangle className="text-red-400" size={24} />
          <h3 className="text-xl font-bold text-red-400">Danger Zone</h3>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-red-900/10 border border-red-500/20 rounded-lg">
            <div className="text-white font-medium mb-2">Delete Account</div>
            <div className="text-gray-400 text-sm mb-3">
              Permanently delete your account and all associated data. This action cannot be undone.
            </div>
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
            >
              Delete Account
            </button>
          </div>
        </div>
      </div>

      {/* Save Settings Button */}
      <div className="flex justify-end">
        <button className="flex items-center space-x-2 bg-yellow-500 hover:bg-yellow-600 text-black px-6 py-3 rounded-md font-semibold transition-colors">
          <Save size={20} />
          <span>Save All Settings</span>
        </button>
      </div>

      {/* Password Change Modal */}
      {showPasswordChange && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20 max-w-md w-full">
            <h3 className="text-xl font-bold text-yellow-400 mb-4">Change Password</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
                <input
                  type="password"
                  value={passwordData.current}
                  onChange={(e) => setPasswordData({ ...passwordData, current: e.target.value })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">New Password</label>
                <input
                  type="password"
                  value={passwordData.new}
                  onChange={(e) => setPasswordData({ ...passwordData, new: e.target.value })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
                <input
                  type="password"
                  value={passwordData.confirm}
                  onChange={(e) => setPasswordData({ ...passwordData, confirm: e.target.value })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowPasswordChange(false)}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handlePasswordChange}
                className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-md transition-colors"
              >
                Change Password
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-lg p-6 border border-red-500/30 max-w-md w-full">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="text-red-400" size={24} />
              <h3 className="text-xl font-bold text-red-400">Delete Account</h3>
            </div>

            <p className="text-gray-300 mb-6">
              Are you sure you want to delete your account? This action is permanent and cannot be undone. All your
              data, orders, and settings will be permanently removed.
            </p>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Delete account logic here
                  console.log("Account deletion requested")
                  setShowDeleteConfirm(false)
                }}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors"
              >
                Delete Account
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
