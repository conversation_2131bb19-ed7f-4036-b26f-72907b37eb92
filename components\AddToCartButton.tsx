"use client"

import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { ShoppingCart, Check } from "lucide-react"

interface AddToCartButtonProps {
  productId: string
  productType: "account" | "uc" | "hack"
  price: { egp: number; usd: number }
  disabled?: boolean
}

export default function AddToCartButton({ productId, productType, price, disabled }: AddToCartButtonProps) {
  const { t } = useLanguage()
  const [isAdded, setIsAdded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleAddToCart = async () => {
    setIsLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    setIsAdded(true)
    setIsLoading(false)

    // Reset after 2 seconds
    setTimeout(() => setIsAdded(false), 2000)
  }

  return (
    <div className="space-y-4">
      <div className="bg-gray-800 rounded-lg p-4 border border-yellow-500/20">
        <div className="text-center mb-4">
          <div className="text-2xl font-bold text-yellow-400">
            {price.egp} {t("egp")}
          </div>
          <div className="text-gray-400">
            ${price.usd} {t("usd")}
          </div>
        </div>

        <button
          onClick={handleAddToCart}
          disabled={disabled || isLoading || isAdded}
          className={`w-full flex items-center justify-center space-x-2 py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
            isAdded
              ? "bg-green-500 text-white"
              : disabled
                ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                : "bg-yellow-500 hover:bg-yellow-600 text-black hover:transform hover:scale-105"
          }`}
        >
          {isLoading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
          ) : isAdded ? (
            <>
              <Check size={20} />
              <span>Added to Cart</span>
            </>
          ) : (
            <>
              <ShoppingCart size={20} />
              <span>Add to Cart</span>
            </>
          )}
        </button>
      </div>
    </div>
  )
}
