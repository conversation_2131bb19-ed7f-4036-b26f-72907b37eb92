"use client";
import React, { useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export const BoxesCore = ({ className, ...rest }: { className?: string }) => {
  // Reduce the number of boxes for better performance
  const rows = new Array(100).fill(1);
  const cols = new Array(60).fill(1);
  
  // Dark color palette (black and gray tones)
  const colors = useMemo(() => [
    "rgb(23 23 23)", // nearly black
    "rgb(38 38 38)", // very dark gray
    "rgb(28 28 28)", // dark gray
    "rgb(64 64 64)", // medium gray
    "rgb(82 82 82)", // gray
    "rgb(15 15 15)", // almost black
    "rgb(31 41 55)", // dark slate
    "rgb(17 24 39)", // darker slate
    "rgb(30 30 30)", // dark gray
  ], []);

  const getRandomColor = useCallback(() => {
    return colors[Math.floor(Math.random() * colors.length)];
  }, [colors]);

  // Only render every nth box with a plus icon to reduce SVG rendering
  const shouldRenderIcon = useCallback((i: number, j: number) => {
    return j % 6 === 0 && i % 6 === 0;
  }, []);

  // Used for virtualization - only render boxes that might be visible
  const renderRow = useCallback((rowIndex: number) => (
    <motion.div
      key={`row-${rowIndex}`}
      className="w-16 h-8 border-l border-zinc-800 relative"
      initial={false}
    >
      {cols.map((_, colIndex) => (
        <motion.div
          whileHover={{
            backgroundColor: getRandomColor(),
            transition: { duration: 0 },
          }}
          transition={{ duration: 0.5 }}
          key={`col-${rowIndex}-${colIndex}`}
          className="w-16 h-8 border-r border-t border-zinc-800 relative"
          initial={false}
        >
          {shouldRenderIcon(rowIndex, colIndex) ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="absolute h-6 w-10 -top-[14px] -left-[22px] text-zinc-800 stroke-[1px] pointer-events-none"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 6v12m6-6H6"
              />
            </svg>
          ) : null}
        </motion.div>
      ))}
    </motion.div>
  ), [cols, getRandomColor, shouldRenderIcon]);

  // Memoize the rows for better performance
  const renderedRows = useMemo(() => rows.map((_, i) => renderRow(i)), [rows, renderRow]);

  return (
    <div
      style={{
        transform: `translate(0%,0%) skewX(-48deg) skewY(14deg) scale(1.2) rotate(0deg) translateZ(0)`,
        willChange: "transform",
      }}
      className={cn(
        "absolute inset-0 flex w-full h-full z-0",
        className
      )}
      {...rest}
    >
      {renderedRows}
    </div>
  );
};

// Using memo for the entire component
export const Boxes = React.memo(BoxesCore); 