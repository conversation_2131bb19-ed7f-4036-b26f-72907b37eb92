[debug] [2025-07-12T22:52:27.900Z] ----------------------------------------------------------------------
[debug] [2025-07-12T22:52:27.905Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js login
[debug] [2025-07-12T22:52:27.906Z] CLI Version:   14.10.1
[debug] [2025-07-12T22:52:27.906Z] Platform:      win32
[debug] [2025-07-12T22:52:27.906Z] Node Version:  v22.17.0
[debug] [2025-07-12T22:52:27.906Z] Time:          Sun Jul 13 2025 01:52:27 GMT+0300 (Eastern European Summer Time)
[debug] [2025-07-12T22:52:27.906Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-12T22:52:27.909Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[info] i  The Firebase CLI’s MCP server feature can optionally make use of Gemini in Firebase. Learn more about Gemini in Firebase and how it uses your data: https://firebase.google.com/docs/gemini-in-firebase#how-gemini-in-firebase-uses-your-data 
[debug] [2025-07-12T22:52:28.497Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-12T22:52:28.497Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[info] 
[info] i  Firebase optionally collects CLI and Emulator Suite usage and error reporting information to help improve our products. Data is collected in accordance with Google's privacy policy (https://policies.google.com/privacy) and is not used to identify you. 
