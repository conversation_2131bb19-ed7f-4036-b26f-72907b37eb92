"use client"

import React from "react";
import { motion } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import * as LucideIcons from "lucide-react";

interface Feature {
  icon: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
}

interface WhyChooseUsProps {
  features: Feature[];
  title?: { en: string; ar: string };
  subtitle?: { en: string; ar: string };
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const WhyChooseUs = ({ features, title, subtitle }: WhyChooseUsProps) => {
  const { language } = useLanguage();

  const defaultTitle = { en: "Why Choose RNG-STORE?", ar: "لماذا تختار RNG-STORE؟" };
  const defaultSubtitle = { 
    en: "We provide the best gaming experience with premium quality and unmatched service", 
    ar: "نحن نقدم أفضل تجربة ألعاب بجودة مميزة وخدمة لا مثيل لها" 
  };

  return (
    <section className="py-20 px-4 bg-card/30 w-full">
      <div className="container mx-auto">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={fadeInUp}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold text-white mb-4">
            {(title || defaultTitle)[language]}
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            {(subtitle || defaultSubtitle)[language]}
          </p>
        </motion.div>

        <motion.div 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerContainer}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {features.map((feature, index) => {
            const IconComponent = (LucideIcons as any)[feature.icon.charAt(0).toUpperCase() + feature.icon.slice(1)];
            return (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="bg-card/40 p-6 rounded-xl border border-border/50 hover:border-pubg-orange/30 transition-colors"
              >
                <div className="mb-4 w-12 h-12 bg-pubg-orange/20 rounded-full flex items-center justify-center">
                  <IconComponent className="text-pubg-orange w-6 h-6" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  {feature.title[language]}
                </h3>
                <p className="text-muted-foreground text-sm">
                  {feature.description[language]}
                </p>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs; 