"use client"

import React, { useState } from "react";
import { motion } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Mail } from "lucide-react";

interface NewsletterProps {
  title?: { en: string; ar: string };
  description?: { en: string; ar: string };
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const Newsletter = ({ title, description }: NewsletterProps) => {
  const { language, t } = useLanguage();
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);

  const defaultTitle = { en: "Stay Updated", ar: "ابق على اطلاع" };
  const defaultDescription = { 
    en: "Subscribe to our newsletter and get the latest updates on new accounts, UC packages, and exclusive offers", 
    ar: "اشترك في نشرتنا الإخبارية واحصل على آخر التحديثات حول الحسابات الجديدة وحزم اليو سي والعروض الحصرية" 
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      // Here you would typically send the email to your backend
      setIsSubscribed(true);
      setEmail("");
      setTimeout(() => setIsSubscribed(false), 3000);
    }
  };

  return (
    <motion.section
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={staggerContainer}
      className="py-16 relative z-10"
    >
      <div className="container mx-auto px-4">
        <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-8 max-w-3xl mx-auto">
          <motion.div
            variants={fadeInUp}
            className="text-center mb-6"
          >
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-pubg-orange/20 rounded-full flex items-center justify-center">
                <Mail className="w-8 h-8 text-pubg-orange" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">
              {(title || defaultTitle)[language]}
            </h2>
            <p className="text-muted-foreground">
              {(description || defaultDescription)[language]}
            </p>
          </motion.div>
          
          <motion.form 
            variants={fadeInUp}
            onSubmit={handleSubmit}
            className="flex flex-col sm:flex-row gap-3"
          >
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1 bg-background border-border focus:ring-2 focus:ring-pubg-orange"
              placeholder={language === 'en' ? "Enter your email" : "أدخل بريدك الإلكتروني"}
              required
            />
            <Button 
              type="submit"
              className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
              disabled={isSubscribed}
            >
              {isSubscribed 
                ? (language === 'en' ? "Subscribed!" : "تم الاشتراك!")
                : (language === 'en' ? "Subscribe" : "اشترك")
              }
            </Button>
          </motion.form>
          
          {isSubscribed && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 text-center text-green-400"
            >
              {language === 'en' 
                ? "Thank you for subscribing! You'll receive updates soon." 
                : "شكراً لك على الاشتراك! ستتلقى التحديثات قريباً."
              }
            </motion.div>
          )}
        </div>
      </div>
    </motion.section>
  );
};

export default Newsletter; 