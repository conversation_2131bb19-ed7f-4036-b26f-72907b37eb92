#!/usr/bin/env node

/**
 * Firebase Setup Script
 * Helps with initial Firebase CLI setup and project configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 Firebase CLI Setup Script');
console.log('============================\n');

// Check if Firebase CLI is installed
try {
  execSync('firebase --version', { stdio: 'pipe' });
  console.log('✅ Firebase CLI is installed');
} catch (error) {
  console.log('❌ Firebase CLI not found. Installing...');
  try {
    execSync('npm install -g firebase-tools', { stdio: 'inherit' });
    console.log('✅ Firebase CLI installed successfully');
  } catch (installError) {
    console.error('❌ Failed to install Firebase CLI:', installError.message);
    process.exit(1);
  }
}

// Check if user is logged in
try {
  const result = execSync('firebase auth:list', { stdio: 'pipe' });
  console.log('✅ You are logged in to Firebase');
} catch (error) {
  console.log('⚠️  You are not logged in to Firebase');
  console.log('Please run: npm run firebase:login');
  console.log('Or manually: firebase login\n');
}

// Check project configuration
const firebaseRcPath = path.join(process.cwd(), '.firebaserc');
if (fs.existsSync(firebaseRcPath)) {
  console.log('✅ Firebase project configuration found');
  
  try {
    const config = JSON.parse(fs.readFileSync(firebaseRcPath, 'utf8'));
    console.log(`📁 Default project: ${config.projects.default}`);
  } catch (error) {
    console.log('⚠️  Could not read .firebaserc file');
  }
} else {
  console.log('❌ Firebase project configuration not found');
}

// Check Firebase configuration files
const configFiles = [
  'firebase.json',
  'firestore.rules',
  'firestore.indexes.json',
  'storage.rules'
];

console.log('\n📋 Configuration Files:');
configFiles.forEach(file => {
  const exists = fs.existsSync(path.join(process.cwd(), file));
  console.log(`${exists ? '✅' : '❌'} ${file}`);
});

console.log('\n🚀 Next Steps:');
console.log('1. Login to Firebase: npm run firebase:login');
console.log('2. Verify project: firebase use test-b95aa');
console.log('3. Test with emulators: npm run firebase:emulators');
console.log('4. Deploy rules: npm run firebase:deploy:rules');
console.log('5. Deploy indexes: npm run firebase:deploy:indexes');

console.log('\n📚 For more help, see FIREBASE_SETUP.md');
