rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() && 
             exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
             get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    function hasAnyRole(roles) {
      return isAuthenticated() && 
             exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
             get(/databases/(default)/documents/users/$(request.auth.uid)).data.role in roles;
    }
    
    function isAdmin() {
      return hasAnyRole(['admin', 'super_admin']);
    }
    
    function isModerator() {
      return hasAnyRole(['moderator', 'admin', 'super_admin']);
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    function isValidDocumentFile() {
      return request.resource.contentType in ['application/pdf', 'text/plain'] &&
             request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // Public files (anyone can read)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if isModerator() && isValidImageFile();
    }
    
    // Product images (anyone can read, moderators can write)
    match /products/{productId}/{allPaths=**} {
      allow read: if true;
      allow write: if isModerator() && isValidImageFile();
    }
    
    // User profile images
    match /users/{userId}/profile/{allPaths=**} {
      allow read: if true;
      allow write: if (isOwner(userId) || isAdmin()) && isValidImageFile();
    }
    
    // User private files
    match /users/{userId}/private/{allPaths=**} {
      allow read, write: if isOwner(userId) && 
                            (isValidImageFile() || isValidDocumentFile());
    }
    
    // Order attachments
    match /orders/{orderId}/{allPaths=**} {
      allow read: if isAuthenticated() && 
                     (exists(/databases/(default)/documents/orders/$(orderId)) &&
                      get(/databases/(default)/documents/orders/$(orderId)).data.userId == request.auth.uid ||
                      isModerator());
      allow write: if isAuthenticated() && 
                      exists(/databases/(default)/documents/orders/$(orderId)) &&
                      get(/databases/(default)/documents/orders/$(orderId)).data.userId == request.auth.uid &&
                      (isValidImageFile() || isValidDocumentFile());
    }
    
    // Admin files
    match /admin/{allPaths=**} {
      allow read, write: if isAdmin();
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if isOwner(userId) && 
                            (isValidImageFile() || isValidDocumentFile());
      // Auto-delete after 24 hours (handled by Cloud Functions)
    }
    
    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
