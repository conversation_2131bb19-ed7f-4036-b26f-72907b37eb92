"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { DatabaseService } from '@/lib/database';
import { Product, ProductType, ProductFilters } from '@/lib/types';
import { formatPrice, getProductTypeLabel, getProductStatusColor, isProductInStock, isProductLowStock } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Copy, 
  Download, 
  Upload, 
  Eye, 
  EyeOff,
  Star,
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface ProductManagerProps {
  initialProducts?: Product[];
}

export default function ProductManager({ initialProducts = [] }: ProductManagerProps) {
  const { canManageProducts } = useAuth();
  const { t } = useLanguage();
  
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(initialProducts);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<ProductType>(ProductType.HACK);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ProductFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);

  // Load products
  const loadProducts = useCallback(async () => {
    if (!canManageProducts) return;
    
    setLoading(true);
    try {
      const response = await DatabaseService.getProducts(filters);
      setProducts(response.data);
      setFilteredProducts(response.data);
    } catch (error) {
      console.error('Error loading products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  }, [filters, canManageProducts]);

  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  // Filter products by type and search
  useEffect(() => {
    let filtered = products.filter(product => product.type === activeTab);
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.en.toLowerCase().includes(term) ||
        product.name.ar.toLowerCase().includes(term) ||
        product.description.en.toLowerCase().includes(term) ||
        product.description.ar.toLowerCase().includes(term)
      );
    }
    
    setFilteredProducts(filtered);
  }, [products, activeTab, searchTerm]);

  // Handle product selection
  const handleSelectProduct = (productId: string, selected: boolean) => {
    if (selected) {
      setSelectedProducts(prev => [...prev, productId]);
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedProducts(filteredProducts.map(p => p.id));
    } else {
      setSelectedProducts([]);
    }
  };

  // Bulk operations
  const handleBulkDelete = async () => {
    if (selectedProducts.length === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedProducts.length} products?`)) return;
    
    try {
      await DatabaseService.bulkDeleteProducts(selectedProducts);
      await loadProducts();
      setSelectedProducts([]);
      toast.success(`${selectedProducts.length} products deleted successfully`);
    } catch (error) {
      console.error('Error deleting products:', error);
      toast.error('Failed to delete products');
    }
  };

  const handleBulkToggleActive = async (isActive: boolean) => {
    if (selectedProducts.length === 0) return;
    
    try {
      const updates = selectedProducts.map(id => ({
        id,
        data: { isActive, updatedBy: 'current-user-id' }
      }));
      
      await DatabaseService.bulkUpdateProducts(updates);
      await loadProducts();
      setSelectedProducts([]);
      toast.success(`${selectedProducts.length} products ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Error updating products:', error);
      toast.error('Failed to update products');
    }
  };

  const handleBulkToggleFeatured = async (isFeatured: boolean) => {
    if (selectedProducts.length === 0) return;
    
    try {
      const updates = selectedProducts.map(id => ({
        id,
        data: { isFeatured, updatedBy: 'current-user-id' }
      }));
      
      await DatabaseService.bulkUpdateProducts(updates);
      await loadProducts();
      setSelectedProducts([]);
      toast.success(`${selectedProducts.length} products ${isFeatured ? 'featured' : 'unfeatured'} successfully`);
    } catch (error) {
      console.error('Error updating products:', error);
      toast.error('Failed to update products');
    }
  };

  // Export products
  const handleExport = () => {
    const dataToExport = selectedProducts.length > 0 
      ? products.filter(p => selectedProducts.includes(p.id))
      : filteredProducts;
    
    const dataStr = JSON.stringify(dataToExport, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `products-${activeTab}-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
    
    toast.success('Products exported successfully');
  };

  // Delete single product
  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return;
    
    try {
      await DatabaseService.deleteProduct(productId);
      await loadProducts();
      toast.success('Product deleted successfully');
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    }
  };

  // Toggle product status
  const handleToggleProductStatus = async (product: Product, field: 'isActive' | 'isFeatured') => {
    try {
      await DatabaseService.updateProduct(product.id, {
        [field]: !product[field],
        updatedBy: 'current-user-id'
      });
      await loadProducts();
      toast.success(`Product ${field === 'isActive' ? 'status' : 'featured status'} updated`);
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error('Failed to update product');
    }
  };

  if (!canManageProducts) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You don't have permission to manage products.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-yellow-400">Product Management</h1>
          <p className="text-gray-400">Manage your gaming products inventory</p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => setIsAddingProduct(true)}
            className="bg-yellow-500 hover:bg-yellow-600 text-black"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Product
          </Button>
          
          <Button
            onClick={handleExport}
            variant="outline"
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </div>
          
          {showFilters && (
            <div className="mt-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="premium">Premium</SelectItem>
                    <SelectItem value="basic">Basic</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Stock" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Stock</SelectItem>
                    <SelectItem value="in-stock">In Stock</SelectItem>
                    <SelectItem value="low-stock">Low Stock</SelectItem>
                    <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button variant="outline" onClick={() => setFilters({})}>
                  Clear Filters
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap items-center gap-4">
              <span className="text-sm text-gray-600">
                {selectedProducts.length} products selected
              </span>
              
              <div className="flex flex-wrap gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkToggleActive(true)}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Activate
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkToggleActive(false)}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Deactivate
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkToggleFeatured(true)}
                >
                  <Star className="mr-2 h-4 w-4" />
                  Feature
                </Button>
                
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={handleBulkDelete}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Product Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as ProductType)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value={ProductType.HACK}>
            Game Hacks ({products.filter(p => p.type === ProductType.HACK).length})
          </TabsTrigger>
          <TabsTrigger value={ProductType.UC}>
            UC Packages ({products.filter(p => p.type === ProductType.UC).length})
          </TabsTrigger>
          <TabsTrigger value={ProductType.ACCOUNT}>
            Gaming Accounts ({products.filter(p => p.type === ProductType.ACCOUNT).length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto"></div>
              <p className="mt-2 text-gray-400">Loading products...</p>
            </div>
          ) : filteredProducts.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No products found</h3>
                <p className="text-gray-400 mb-4">
                  {searchTerm ? 'No products match your search criteria.' : `No ${getProductTypeLabel(activeTab).toLowerCase()}s have been added yet.`}
                </p>
                <Button
                  onClick={() => setIsAddingProduct(true)}
                  className="bg-yellow-500 hover:bg-yellow-600 text-black"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add First Product
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {/* Select All */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedProducts.length === filteredProducts.length}
                  onCheckedChange={handleSelectAll}
                />
                <label className="text-sm text-gray-600">
                  Select all ({filteredProducts.length} products)
                </label>
              </div>

              {/* Products List */}
              {filteredProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  selected={selectedProducts.includes(product.id)}
                  onSelect={(selected) => handleSelectProduct(product.id, selected)}
                  onEdit={() => setEditingProduct(product)}
                  onDelete={() => handleDeleteProduct(product.id)}
                  onToggleActive={() => handleToggleProductStatus(product, 'isActive')}
                  onToggleFeatured={() => handleToggleProductStatus(product, 'isFeatured')}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Product Card Component
interface ProductCardProps {
  product: Product;
  selected: boolean;
  onSelect: (selected: boolean) => void;
  onEdit: () => void;
  onDelete: () => void;
  onToggleActive: () => void;
  onToggleFeatured: () => void;
}

function ProductCard({
  product,
  selected,
  onSelect,
  onEdit,
  onDelete,
  onToggleActive,
  onToggleFeatured
}: ProductCardProps) {
  const statusColor = getProductStatusColor(product);
  const inStock = isProductInStock(product);
  const lowStock = isProductLowStock(product);

  return (
    <Card className={`transition-all ${selected ? 'ring-2 ring-yellow-500' : ''}`}>
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <Checkbox
            checked={selected}
            onCheckedChange={onSelect}
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold text-white truncate">
                {product.name.en}
              </h3>
              
              {product.isFeatured && (
                <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400">
                  <Star className="mr-1 h-3 w-3" />
                  Featured
                </Badge>
              )}
              
              <Badge variant={product.isActive ? "default" : "destructive"}>
                {product.isActive ? 'Active' : 'Inactive'}
              </Badge>
              
              {lowStock && (
                <Badge variant="destructive">
                  <AlertTriangle className="mr-1 h-3 w-3" />
                  Low Stock
                </Badge>
              )}
            </div>
            
            <p className="text-gray-400 text-sm mb-2">{product.name.ar}</p>
            <p className="text-gray-300 text-sm mb-3 line-clamp-2">
              {product.description.en}
            </p>
            
            <div className="flex flex-wrap items-center gap-4 text-sm">
              <span className="font-semibold text-yellow-400">
                {formatPrice(product.price, 'egp')}
              </span>
              <span className="font-semibold text-green-400">
                {formatPrice(product.price, 'usd')}
              </span>
              <span className={statusColor}>
                Stock: {product.stockCount}
              </span>
              <span className="text-gray-400">
                Sold: {product.totalSold}
              </span>
              {product.images.length > 0 && (
                <span className="text-gray-400">
                  {product.images.length} images
                </span>
              )}
            </div>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button
              size="sm"
              variant="outline"
              onClick={onEdit}
            >
              <Edit className="h-4 w-4" />
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={onToggleActive}
            >
              {product.isActive ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={onToggleFeatured}
            >
              <Star className={`h-4 w-4 ${product.isFeatured ? 'fill-current' : ''}`} />
            </Button>
            
            <Button
              size="sm"
              variant="destructive"
              onClick={onDelete}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
