import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Cairo } from "next/font/google"
import "./globals.css"
import { LanguageProvider } from "@/contexts/LanguageContext"
import SimpleLoader from "@/components/SimpleLoader"
import Navbar from "@/components/Navbar"
import Footer from "@/components/Footer"
import { preloadCriticalResources } from "@/lib/performance"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/contexts/AuthContext"
import { Toaster } from "@/components/ui/toaster"

// Optimized font loading with variable fonts
const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  variable: '--font-inter',
})

const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  display: 'swap',
  preload: true,
  variable: '--font-cairo',
})

export const metadata: Metadata = {
  title: "RNG VIP - PUBG Accounts, UC & Hacks",
  description: "Premium PUBG accounts, UC packages, and gaming tools. Your ultimate gaming destination.",
  keywords: "PUBG, accounts, UC, hacks, gaming, mobile gaming",
  authors: [{ name: "RNG VIP" }],
  openGraph: {
    title: "RNG VIP - PUBG Gaming Store",
    description: "Premium PUBG accounts, UC packages, and gaming tools",
    type: "website",
  },
  icons: {
    icon: "/placeholder-logo.png",
  },
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Preload critical resources on mount
  if (typeof window !== 'undefined') {
    preloadCriticalResources()
  }

  return (
    <html lang="en" className={`${inter.variable} ${cairo.variable}`} suppressHydrationWarning>
      <head>
        {/* Preconnect to external domains for faster loading */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* DNS prefetch for potential external resources */}
        <link rel="dns-prefetch" href="https://placeholder.svg" />
        
        {/* Preload critical resources */}
        <link rel="preload" href="/placeholder-logo.png" as="image" />
        <link rel="preload" href="/placeholder.svg" as="image" />
        
        {/* Resource hints for better performance */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="theme-color" content="#111827" />
      </head>
      <body className={`${inter.className} bg-gray-900 text-white min-h-screen antialiased`}>
        <LanguageProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <AuthProvider>
              <Navbar />
              <div className="flex flex-col min-h-screen pt-16">
                <main className="flex-1">
                  {children}
                </main>
                <Footer />
              </div>
              <Toaster />
            </AuthProvider>
          </ThemeProvider>
        </LanguageProvider>
      </body>
    </html>
  )
}
