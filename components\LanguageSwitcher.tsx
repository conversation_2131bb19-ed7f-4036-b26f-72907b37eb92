"use client"

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { ChevronDown, Check } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const FLAGS = {
  en: "/placeholder.svg", // You can replace with actual flag images
  ar: "/placeholder.svg"
};

// Create a global variable to track language change state
export const isChangingLanguageGlobal = {
  value: false
};

// Expose to window for access in HTML
if (typeof window !== 'undefined') {
  (window as any).isChangingLanguageGlobal = isChangingLanguageGlobal;
}

export const LanguageSwitcher = () => {
  const { language: currentLanguage, toggleLanguage, t } = useLanguage();
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.lang-switcher-container')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const changeLanguage = (lng: string) => {
    // Don't do anything if already changing language or selecting current language
    if (isChangingLanguage || lng === currentLanguage) return;
    
    // Set loading state
    setIsChangingLanguage(true);
    isChangingLanguageGlobal.value = true;
    setIsAnimating(true);
    
    // Set language direction
    document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
    
    // Save the language preference
    localStorage.setItem('language', lng);
    
    // Close menu
    setIsOpen(false);
    
    // Change language
    toggleLanguage();
    
    // Reset states after animation
    setTimeout(() => {
      setIsChangingLanguage(false);
      isChangingLanguageGlobal.value = false;
    }, 800);
  };

  // Animation variants
  const switcherVariants: Variants = {
    initial: { scale: 1 },
    hover: { scale: 1.05, transition: { duration: 0.2 } },
    tap: { scale: 0.97, transition: { duration: 0.1 } },
    active: { backgroundColor: "rgba(255, 255, 255, 0.1)", boxShadow: "0 0 15px rgba(255, 255, 255, 0.15)" }
  };

  const iconVariants: Variants = {
    initial: { rotate: 0 },
    active: { rotate: 180, transition: { duration: 0.3 } },
  };

  const menuVariants: Variants = {
    hidden: { 
      opacity: 0, 
      y: -10, 
      scale: 0.95, 
      transformOrigin: "top center"
    },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1, 
      transformOrigin: "top center",
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 20,
        duration: 0.2
      }
    },
    exit: { 
      opacity: 0, 
      y: -10, 
      scale: 0.95,
      transition: { duration: 0.15 } 
    }
  };

  const flagVariants: Variants = {
    initial: { scale: 1, rotate: 0 },
    hover: { scale: 1.1, transition: { duration: 0.2 } },
    changing: { 
      rotateY: [0, 180, 360],
      scale: [1, 1.2, 1],
      transition: { 
        rotateY: { duration: 0.8, ease: "easeInOut" },
        scale: { duration: 0.8, ease: "easeInOut" }
      }
    },
    menuOpen: {
      rotate: [0, -5, 5, -3, 3, 0],
      scale: [1, 1.15, 1.1],
      transition: {
        rotate: { duration: 0.5, ease: "easeInOut" },
        scale: { duration: 0.4, ease: "easeOut" }
      }
    }
  };

  const itemVariants: Variants = {
    initial: { opacity: 0, x: -20 },
    animate: (custom: number) => ({
      opacity: 1,
      x: 0,
      transition: { 
        delay: custom * 0.1,
        duration: 0.3,
        ease: "easeOut"
      }
    }),
    exit: { opacity: 0, x: 20, transition: { duration: 0.2 } },
    hover: { 
      backgroundColor: "rgba(255, 255, 255, 0.08)",
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.98, transition: { duration: 0.1 } }
  };

  return (
    <div className="lang-switcher-container relative z-50">
      {/* Main Switcher Button */}
      <motion.div
        className="neo-lang-switcher flex items-center gap-2 px-2.5 py-2 rounded-full cursor-pointer"
        onClick={() => !isChangingLanguage && setIsOpen(!isOpen)}
        variants={switcherVariants}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
        animate={isOpen ? "active" : isAnimating ? { scale: [1, 1.1, 1] } : "initial"}
        transition={{ duration: 0.5 }}
      >
        {/* Flag */}
        <motion.div 
          className="relative flag-container"
          variants={flagVariants}
          initial="initial"
          whileHover={!isAnimating && !isOpen ? "hover" : ""}
          animate={isAnimating ? "changing" : isOpen ? "menuOpen" : "initial"}
          onAnimationComplete={() => setIsAnimating(false)}
        >
          <motion.div className="absolute inset-0 rounded-md glow-effect" />
          <div className="h-5 w-5 rounded-sm bg-pubg-orange/20 border border-pubg-orange/30 flex items-center justify-center">
            <span className="text-xs font-bold text-pubg-orange">
              {currentLanguage === 'ar' ? 'ع' : 'EN'}
            </span>
          </div>
        </motion.div>

        {/* Text */}
        <span className="text-sm font-medium text-white hidden sm:inline">
          {currentLanguage === 'ar' ? 'العربية' : 'English'}
        </span>

        {/* Chevron */}
        <motion.div
          variants={iconVariants}
          animate={isOpen ? "active" : "initial"}
        >
          <ChevronDown className="h-4 w-4 text-white/70" />
        </motion.div>
      </motion.div>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute mt-2 right-0 w-32 neo-lang-menu rounded-xl overflow-hidden"
            variants={menuVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{ 
              pointerEvents: isOpen ? "auto" : "none" 
            }}
          >
            <div className="neo-lang-menu-header py-2 px-2 border-b border-white/5">
              <div className="text-xs font-medium text-white/70 text-center">
                Switch Language
              </div>
            </div>

            <div className="py-1">
              {/* English Option */}
              <motion.div
                className={`neo-lang-item flex items-center gap-2 px-3 py-2 ${
                  currentLanguage === 'en' ? 'neo-lang-active' : ''
                } ${isChangingLanguage ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
                onClick={() => !isChangingLanguage && changeLanguage('en')}
                variants={itemVariants}
                custom={0}
                initial="initial"
                animate="animate"
                exit="exit"
                whileHover={!isChangingLanguage ? "hover" : undefined}
                whileTap={!isChangingLanguage ? "tap" : undefined}
              >
                <div className="h-4 w-4 rounded-sm bg-pubg-orange/20 border border-pubg-orange/30 flex items-center justify-center">
                  <span className="text-xs font-bold text-pubg-orange">EN</span>
                </div>
                <span className="text-sm text-white/90 flex-1">English</span>
                {currentLanguage === 'en' && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <Check className="h-3 w-3 text-pubg-orange" />
                  </motion.div>
                )}
              </motion.div>

              {/* Arabic Option */}
              <motion.div
                className={`neo-lang-item flex items-center gap-2 px-3 py-2 ${
                  currentLanguage === 'ar' ? 'neo-lang-active' : ''
                } ${isChangingLanguage ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
                onClick={() => !isChangingLanguage && changeLanguage('ar')}
                variants={itemVariants}
                custom={1}
                initial="initial"
                animate="animate"
                exit="exit"
                whileHover={!isChangingLanguage ? "hover" : undefined}
                whileTap={!isChangingLanguage ? "tap" : undefined}
              >
                <div className="h-4 w-4 rounded-sm bg-pubg-orange/20 border border-pubg-orange/30 flex items-center justify-center">
                  <span className="text-xs font-bold text-pubg-orange">ع</span>
                </div>
                <span className="text-sm text-white/90 flex-1">العربية</span>
                {currentLanguage === 'ar' && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <Check className="h-3 w-3 text-pubg-orange" />
                  </motion.div>
                )}
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSwitcher; 