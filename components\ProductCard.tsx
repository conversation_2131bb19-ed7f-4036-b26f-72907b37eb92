"use client"

import Image from "next/image"
import { useLanguage } from "@/contexts/LanguageContext"
import Link from "next/link"
import { memo } from "react"

interface ProductCardProps {
  id: string
  name: { en: string; ar: string }
  description: { en: string; ar: string }
  price: { egp: number; usd: number }
  image: string
  category?: { en: string; ar: string }
  quantity?: number
  type: "account" | "uc" | "hack"
}

const ProductCard = memo(function ProductCard({
  id,
  name,
  description,
  price,
  image,
  category,
  quantity,
  type,
}: ProductCardProps) {
  const { language, t } = useLanguage()

  return (
    <Link href={`/${type === "uc" ? "uc" : type + "s"}/${id}`} className="block" prefetch={true}>
      <article className="bg-gray-800 rounded-lg overflow-hidden border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300 hover:transform hover:scale-105 hardware-accelerated">
        <div className="relative h-48 overflow-hidden">
          <Image 
            src={image || "/placeholder.svg"} 
            alt={name[language]} 
            fill 
            className="object-cover transition-transform duration-300 hover:scale-110" 
            loading="lazy"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            quality={80}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
          {type === "uc" && quantity && (
            <div className="absolute top-2 right-2 bg-yellow-500 text-black px-2 py-1 rounded-md text-sm font-bold">
              {quantity} UC
            </div>
          )}
        </div>

        <div className="p-4">
          <h3 className="text-lg font-semibold text-yellow-400 mb-2 line-clamp-1">{name[language]}</h3>

          {category && (
            <p className="text-sm text-gray-400 mb-2">
              {t("category")}: {category[language]}
            </p>
          )}

          <p className="text-gray-300 text-sm mb-4 line-clamp-2">{description[language]}</p>

          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <div className="text-yellow-400 font-bold">
                {price.egp} {t("egp")}
              </div>
              <div className="text-gray-400 text-sm">
                ${price.usd} {t("usd")}
              </div>
            </div>

            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                // Add to cart logic here
              }}
              className="bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-md font-semibold transition-colors duration-200"
            >
              {t("shopNow")}
            </button>
          </div>
        </div>
      </article>
    </Link>
  )
})

export default ProductCard
