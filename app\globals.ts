// Global performance configurations
export const PERFORMANCE_CONFIG = {
  // Image optimization settings
  IMAGE_QUALITY: 80,
  IMAGE_SIZES: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw',
  
  // Animation settings
  ANIMATION_DURATION: 300,
  ANIMATION_EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  
  // Bundle optimization
  CHUNK_SIZE_LIMIT: 244 * 1024, // 244KB
  
  // Prefetch settings
  PREFETCH_ENABLED: true,
  PREFETCH_ON_HOVER: true,
}

// Static generation settings
export const STATIC_PAGES = [
  '/',
  '/about',
  '/contact',
  '/terms',
  '/privacy'
]

// Force static generation for these routes
export const dynamic = 'force-static'
export const revalidate = false 