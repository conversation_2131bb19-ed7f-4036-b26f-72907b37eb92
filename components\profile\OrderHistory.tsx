"use client"

import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { Package, Calendar, DollarSign, Eye, Download, Filter } from "lucide-react"

interface Order {
  id: string
  product: string
  category: "account" | "uc" | "hack"
  date: string
  price: string
  status: "completed" | "pending" | "cancelled" | "refunded"
  details: string
  quantity?: number
}

export default function OrderHistory() {
  const { t } = useLanguage()
  const [filter, setFilter] = useState<"all" | "completed" | "pending" | "cancelled">("all")
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)

  const orders: Order[] = [
    {
      id: "ORD-001",
      product: "Conqueror Account #1",
      category: "account",
      date: "2024-01-15",
      price: "$50",
      status: "completed",
      details: "High-tier PUBG account with exclusive skins and rare items",
    },
    {
      id: "ORD-002",
      product: "1800 UC Package",
      category: "uc",
      date: "2024-01-10",
      price: "$25",
      status: "completed",
      details: "Perfect for battle pass and skins",
      quantity: 1800,
    },
    {
      id: "ORD-003",
      product: "ESP Hack Pro",
      category: "hack",
      date: "2024-01-05",
      price: "$15",
      status: "completed",
      details: "Advanced ESP with enemy detection through walls",
    },
    {
      id: "ORD-004",
      product: "3500 UC Package",
      category: "uc",
      date: "2024-01-02",
      price: "$45",
      status: "pending",
      details: "Best value UC package for dedicated players",
      quantity: 3500,
    },
    {
      id: "ORD-005",
      product: "Crown Account #2",
      category: "account",
      date: "2023-12-28",
      price: "$30",
      status: "refunded",
      details: "Premium account with multiple legendary skins",
    },
  ]

  const filteredOrders = orders.filter((order) => filter === "all" || order.status === filter)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "cancelled":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "refunded":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "account":
        return "👤"
      case "uc":
        return "💎"
      case "hack":
        return "🔧"
      default:
        return "📦"
    }
  }

  return (
    <div className="space-y-6">
      {/* Order Statistics */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <h3 className="text-xl font-bold text-yellow-400 mb-4">Order Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">
              {orders.filter((o) => o.status === "completed").length}
            </div>
            <div className="text-sm text-gray-400">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">
              {orders.filter((o) => o.status === "pending").length}
            </div>
            <div className="text-sm text-gray-400">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {orders.filter((o) => o.status === "refunded").length}
            </div>
            <div className="text-sm text-gray-400">Refunded</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">{orders.length}</div>
            <div className="text-sm text-gray-400">Total Orders</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 rounded-lg p-4 border border-yellow-500/20">
        <div className="flex items-center space-x-4">
          <Filter size={20} className="text-yellow-400" />
          <span className="text-white font-medium">Filter by status:</span>
          <div className="flex space-x-2">
            {["all", "completed", "pending", "cancelled"].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  filter === status ? "bg-yellow-500 text-black" : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {filteredOrders.map((order) => (
          <div key={order.id} className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start space-x-4">
                <div className="text-2xl">{getCategoryIcon(order.category)}</div>
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">{order.product}</h3>
                    {order.quantity && (
                      <span className="bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-bold">
                        {order.quantity} UC
                      </span>
                    )}
                  </div>
                  <p className="text-gray-400 text-sm mb-2">{order.details}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Package size={14} />
                      <span>Order #{order.id}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar size={14} />
                      <span>{new Date(order.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign size={14} />
                      <span>{order.price}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </span>
                <button
                  onClick={() => setSelectedOrder(order)}
                  className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
                  title="View Details"
                >
                  <Eye size={16} />
                </button>
                {order.status === "completed" && (
                  <button
                    className="p-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
                    title="Download Receipt"
                  >
                    <Download size={16} />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredOrders.length === 0 && (
        <div className="text-center py-12">
          <Package size={48} className="text-gray-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">No orders found</h3>
          <p className="text-gray-500">No orders match the selected filter.</p>
        </div>
      )}

      {/* Order Detail Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20 max-w-md w-full">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-yellow-400">Order Details</h3>
              <button onClick={() => setSelectedOrder(null)} className="text-gray-400 hover:text-white">
                <Eye size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Order ID</label>
                <div className="text-white font-mono">{selectedOrder.id}</div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Product</label>
                <div className="text-white">{selectedOrder.product}</div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Description</label>
                <div className="text-gray-300 text-sm">{selectedOrder.details}</div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Date</label>
                  <div className="text-white">{new Date(selectedOrder.date).toLocaleDateString()}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Price</label>
                  <div className="text-white font-semibold">{selectedOrder.price}</div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Status</label>
                <span
                  className={`inline-block px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(selectedOrder.status)}`}
                >
                  {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                </span>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setSelectedOrder(null)}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Close
              </button>
              {selectedOrder.status === "completed" && (
                <button className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors">
                  Download Receipt
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
