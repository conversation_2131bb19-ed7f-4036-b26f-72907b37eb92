import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { Product, Order, User, Price, LocalizedContent, ProductType, OrderStatus, UserRole } from './types'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Currency formatting
export function formatPrice(price: Price, currency: 'egp' | 'usd' = 'egp'): string {
  const amount = currency === 'egp' ? price.egp : price.usd;
  const symbol = currency === 'egp' ? 'EGP' : '$';

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency === 'egp' ? 'EGP' : 'USD',
    currencyDisplay: 'code'
  }).format(amount).replace(/[A-Z]{3}/, symbol);
}

// Date formatting
export function formatDate(dateString: string, locale: string = 'en-US'): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

export function formatDateShort(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
}

// Text utilities
export function truncateText(text: string, maxLength: number = 100): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

export function getLocalizedText(content: LocalizedContent, locale: 'en' | 'ar' = 'en'): string {
  return content[locale] || content.en || '';
}

// Product utilities
export function getProductTypeLabel(type: ProductType): string {
  const labels = {
    [ProductType.HACK]: 'Game Hack',
    [ProductType.UC]: 'UC Package',
    [ProductType.ACCOUNT]: 'Gaming Account'
  };
  return labels[type];
}

export function getProductStatusColor(product: Product): string {
  if (!product.isActive) return 'text-red-500';
  if (product.stockCount <= product.lowStockThreshold) return 'text-yellow-500';
  return 'text-green-500';
}

export function isProductInStock(product: Product): boolean {
  return product.stockCount > 0;
}

export function isProductLowStock(product: Product): boolean {
  return product.stockCount <= product.lowStockThreshold && product.stockCount > 0;
}

// Order utilities
export function getOrderStatusLabel(status: OrderStatus): string {
  const labels = {
    [OrderStatus.PENDING]: 'Pending',
    [OrderStatus.PROCESSING]: 'Processing',
    [OrderStatus.COMPLETED]: 'Completed',
    [OrderStatus.CANCELLED]: 'Cancelled',
    [OrderStatus.REFUNDED]: 'Refunded'
  };
  return labels[status];
}

export function getOrderStatusColor(status: OrderStatus): string {
  const colors = {
    [OrderStatus.PENDING]: 'text-yellow-500 bg-yellow-500/10',
    [OrderStatus.PROCESSING]: 'text-blue-500 bg-blue-500/10',
    [OrderStatus.COMPLETED]: 'text-green-500 bg-green-500/10',
    [OrderStatus.CANCELLED]: 'text-red-500 bg-red-500/10',
    [OrderStatus.REFUNDED]: 'text-purple-500 bg-purple-500/10'
  };
  return colors[status];
}

export function calculateOrderTotal(order: Order): Price {
  return order.items.reduce(
    (total, item) => ({
      egp: total.egp + (item.totalPrice.egp),
      usd: total.usd + (item.totalPrice.usd)
    }),
    { egp: 0, usd: 0 }
  );
}

// User utilities
export function getUserRoleLabel(role: UserRole): string {
  const labels = {
    [UserRole.CUSTOMER]: 'Customer',
    [UserRole.MODERATOR]: 'Moderator',
    [UserRole.ADMIN]: 'Administrator',
    [UserRole.SUPER_ADMIN]: 'Super Administrator'
  };
  return labels[role];
}

export function getUserRoleColor(role: UserRole): string {
  const colors = {
    [UserRole.CUSTOMER]: 'text-gray-500 bg-gray-500/10',
    [UserRole.MODERATOR]: 'text-blue-500 bg-blue-500/10',
    [UserRole.ADMIN]: 'text-purple-500 bg-purple-500/10',
    [UserRole.SUPER_ADMIN]: 'text-red-500 bg-red-500/10'
  };
  return colors[role];
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2);
}

// File utilities
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

// Array utilities
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

export function sortBy<T>(array: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
  return [...array].sort((a, b) => {
    const aVal = a[key];
    const bVal = b[key];

    if (aVal < bVal) return order === 'asc' ? -1 : 1;
    if (aVal > bVal) return order === 'asc' ? 1 : -1;
    return 0;
  });
}

// Search utilities
export function searchItems<T>(
  items: T[],
  searchTerm: string,
  searchFields: (keyof T)[]
): T[] {
  if (!searchTerm.trim()) return items;

  const term = searchTerm.toLowerCase();

  return items.filter(item =>
    searchFields.some(field => {
      const value = item[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(term);
      }
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(v =>
          typeof v === 'string' && v.toLowerCase().includes(term)
        );
      }
      return false;
    })
  );
}

// Validation utilities
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
  return phoneRegex.test(phone);
}

// URL utilities
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Random utilities
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

export function generateOrderNumber(): string {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `ORD-${timestamp}-${random}`;
}

// Color utilities
export function getRandomColor(): string {
  const colors = [
    'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}

// Performance utilities
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
