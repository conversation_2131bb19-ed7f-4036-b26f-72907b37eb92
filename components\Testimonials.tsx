"use client"

import React from "react";
import { motion } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import { Star } from "lucide-react";

interface Testimonial {
  name: { en: string; ar: string };
  avatar: string;
  rating: number;
  text: { en: string; ar: string };
}

interface TestimonialsProps {
  testimonials: Testimonial[];
  title?: { en: string; ar: string };
  subtitle?: { en: string; ar: string };
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const Testimonials = ({ testimonials, title, subtitle }: TestimonialsProps) => {
  const { language, t } = useLanguage();

  const defaultTitle = { en: "What Our Customers Say", ar: "ماذا يقول عملاؤنا" };
  const defaultSubtitle = { 
    en: "Don't just take our word for it - see what our satisfied customers have to say about their experience", 
    ar: "لا تأخذ كلامنا فقط - اطلع على ما يقوله عملاؤنا الراضون عن تجربتهم" 
  };

  return (
    <section className="py-20 px-4 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-card/30 to-background/90 z-0"></div>
      <div className="container mx-auto relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={fadeInUp}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold text-white mb-4">
            {(title || defaultTitle)[language]}
          </h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {(subtitle || defaultSubtitle)[language]}
          </p>
        </motion.div>

        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerContainer}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={fadeInUp}
              className="glass-card p-6 rounded-xl relative"
            >
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name[language]}
                    className="w-12 h-12 rounded-full mr-4 border-2 border-pubg-orange"
                  />
                  <div>
                    <h4 className="text-white font-bold">{testimonial.name[language]}</h4>
                    <div className="flex">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-500'
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <svg
                    className="absolute top-0 left-0 transform -translate-x-6 -translate-y-8 text-pubg-orange/30 w-16 h-16"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                  <p className="text-muted-foreground relative z-10">
                    {testimonial.text[language]}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials; 