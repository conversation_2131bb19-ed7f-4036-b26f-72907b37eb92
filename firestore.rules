rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    function hasAnyRole(roles) {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in roles;
    }
    
    function isAdmin() {
      return hasAnyRole(['admin', 'super_admin']);
    }
    
    function isModerator() {
      return hasAnyRole(['moderator', 'admin', 'super_admin']);
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read their own data, admins can read all
      allow read: if isOwner(userId) || isAdmin();
      
      // Users can create their own profile during signup
      allow create: if isOwner(userId) && 
                       request.auth.uid == userId &&
                       request.resource.data.role == 'customer';
      
      // Users can update their own data (except role), admins can update all
      allow update: if (isOwner(userId) && 
                       request.resource.data.role == resource.data.role) ||
                       isAdmin();
      
      // Only admins can delete users
      allow delete: if isAdmin();
    }
    
    // Products collection
    match /products/{productId} {
      // Anyone can read products
      allow read: if true;
      
      // Only moderators and admins can create/update/delete products
      allow create, update, delete: if isModerator();
    }
    
    // Orders collection
    match /orders/{orderId} {
      // Users can read their own orders, moderators can read all
      allow read: if isOwner(resource.data.userId) || isModerator();
      
      // Users can create orders for themselves
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.userId;
      
      // Users can update their own pending orders, moderators can update all
      allow update: if (isOwner(resource.data.userId) && 
                       resource.data.status == 'pending') ||
                       isModerator();
      
      // Only admins can delete orders
      allow delete: if isAdmin();
    }
    
    // Analytics collection
    match /analytics/{document} {
      // Only admins can read analytics
      allow read: if isAdmin();
      
      // Only system can write analytics (server-side only)
      allow write: if false;
    }
    
    // Settings collection
    match /settings/{document} {
      // Anyone can read public settings
      allow read: if true;
      
      // Only admins can modify settings
      allow write: if isAdmin();
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
