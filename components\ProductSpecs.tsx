"use client"

import { useLanguage } from "@/contexts/LanguageContext"

interface ProductSpecsProps {
  specs: Record<string, { en: string; ar: string }>
}

export default function ProductSpecs({ specs }: ProductSpecsProps) {
  const { language } = useLanguage()

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
      <h3 className="text-xl font-bold text-yellow-400 mb-4">Specifications</h3>
      <div className="space-y-3">
        {Object.entries(specs).map(([key, value]) => (
          <div key={key} className="flex justify-between items-center py-2 border-b border-gray-700 last:border-b-0">
            <span className="text-gray-300 font-medium capitalize">{key.replace(/([A-Z])/g, " $1")}</span>
            <span className="text-white">{value[language]}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
