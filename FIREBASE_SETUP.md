# Firebase CLI Setup Guide

## 🚀 Quick Start

### 1. Login to Firebase CLI
```bash
firebase login
```
This will open your browser and ask you to authenticate with your Google account.

### 2. Verify Project Connection
```bash
firebase projects:list
```
Make sure you can see your `test-b95aa` project in the list.

### 3. Set Active Project
```bash
firebase use test-b95aa
```

## 📁 Project Structure

Your Firebase configuration files:
- `firebase.json` - Main Firebase configuration
- `.firebaserc` - Project aliases and settings
- `firestore.rules` - Firestore security rules
- `firestore.indexes.json` - Firestore database indexes
- `storage.rules` - Firebase Storage security rules

## 🔧 Available Commands

### Deploy Commands
```bash
# Deploy everything
firebase deploy

# Deploy only Firestore rules
firebase deploy --only firestore:rules

# Deploy only Firestore indexes
firebase deploy --only firestore:indexes

# Deploy only Storage rules
firebase deploy --only storage

# Deploy only hosting
firebase deploy --only hosting
```

### Development Commands
```bash
# Start Firebase emulators
firebase emulators:start

# Start specific emulators
firebase emulators:start --only firestore,auth,storage

# Export emulator data
firebase emulators:export ./emulator-data

# Import emulator data
firebase emulators:start --import ./emulator-data
```

### Rules Testing
```bash
# Test Firestore rules
firebase emulators:exec --only firestore "npm test"

# Validate rules syntax
firebase firestore:rules:validate
```

## 🛡️ Security Rules Overview

### Firestore Rules
- **Users**: Can read/write own data, admins can manage all users
- **Products**: Public read, moderators+ can write
- **Orders**: Users see own orders, moderators see all
- **Analytics**: Admin-only access
- **Settings**: Public read, admin write

### Storage Rules
- **Public files**: Anyone can read, moderators can write
- **Product images**: Public read, moderators can write
- **User profiles**: Public read, users can write own
- **Private files**: User-only access
- **Order attachments**: Order owner and moderators only

## 📊 Database Indexes

Optimized indexes for:
- Product filtering by type, price, status
- Order queries by user, status, date
- User management by role, activity
- Performance-optimized compound queries

## 🔄 NPM Scripts

Add these to your `package.json`:

```json
{
  "scripts": {
    "firebase:login": "firebase login",
    "firebase:deploy": "firebase deploy",
    "firebase:deploy:rules": "firebase deploy --only firestore:rules,storage",
    "firebase:deploy:indexes": "firebase deploy --only firestore:indexes",
    "firebase:emulators": "firebase emulators:start",
    "firebase:emulators:export": "firebase emulators:export ./emulator-data",
    "firebase:emulators:import": "firebase emulators:start --import ./emulator-data",
    "firebase:rules:validate": "firebase firestore:rules:validate"
  }
}
```

## 🚨 Important Notes

### Before First Deploy
1. **Login**: `firebase login`
2. **Verify project**: `firebase use test-b95aa`
3. **Test rules**: Use emulators first
4. **Deploy gradually**: Start with rules, then indexes

### Security Best Practices
- Always test rules in emulators before deploying
- Review rules regularly for security gaps
- Monitor Firebase console for unusual activity
- Use least-privilege principle in rules

### Performance Tips
- Deploy indexes before deploying app code that uses them
- Monitor query performance in Firebase console
- Use composite indexes for complex queries
- Regularly review and optimize unused indexes

## 🔍 Troubleshooting

### Common Issues
1. **Permission denied**: Check if you're logged in and have project access
2. **Rules validation failed**: Check syntax in rules files
3. **Index missing**: Deploy indexes or wait for auto-creation
4. **Emulator connection failed**: Check ports and firewall settings

### Useful Commands
```bash
# Check current user
firebase auth:list

# Check project info
firebase projects:list

# View deployment history
firebase projects:list

# Get help
firebase --help
firebase deploy --help
```

## 📈 Next Steps

1. **Test in Emulators**: Start with `firebase emulators:start`
2. **Deploy Rules**: `firebase deploy --only firestore:rules,storage`
3. **Deploy Indexes**: `firebase deploy --only firestore:indexes`
4. **Monitor**: Check Firebase console for performance and security

---

**Need Help?** Check the [Firebase CLI documentation](https://firebase.google.com/docs/cli) or run `firebase --help`
