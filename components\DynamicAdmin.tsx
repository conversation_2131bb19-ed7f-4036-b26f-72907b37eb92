import dynamic from 'next/dynamic'

// Skeleton components for loading states
const AdminSkeleton = () => (
  <div className="container mx-auto px-4 py-8">
    <div className="animate-pulse">
      <div className="h-8 bg-gray-700 rounded w-1/4 mb-6"></div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="h-32 bg-gray-700 rounded"></div>
        ))}
      </div>
    </div>
  </div>
)

// Dynamic imports for heavy admin components
export const DynamicProductForm = dynamic(() => import('./admin/ProductForm'), {
  loading: () => <AdminSkeleton />,
  ssr: false, // Admin doesn't need SSR
})

export const DynamicProductStats = dynamic(() => import('./admin/ProductStats'), {
  loading: () => <AdminSkeleton />,
  ssr: false,
})

export const DynamicBulkActions = dynamic(() => import('./admin/BulkActions'), {
  loading: () => <AdminSkeleton />,
  ssr: false,
}) 