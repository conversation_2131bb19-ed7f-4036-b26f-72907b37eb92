"use client"

import type React from "react"

import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { Download, Upload, Trash2, Copy } from "lucide-react"

interface Product {
  id: string
  type: "account" | "uc" | "hack"
  name: { en: string; ar: string }
  description: { en: string; ar: string }
  price: { egp: number; usd: number }
  images: string[]
  category?: { en: string; ar: string }
  quantity?: number
  specs?: Record<string, { en: string; ar: string }>
  features?: { en: string[]; ar: string[] }
}

interface BulkActionsProps {
  products: Product[]
  selectedProducts: string[]
  onDeleteSelected: () => void
  onDuplicateSelected: () => void
  onExportSelected: () => void
  onImportProducts: (products: Product[]) => void
}

export default function BulkActions({
  products,
  selectedProducts,
  onDeleteSelected,
  onDuplicateSelected,
  onExportSelected,
  onImportProducts,
}: BulkActionsProps) {
  const { t } = useLanguage()
  const [isImporting, setIsImporting] = useState(false)

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsImporting(true)
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedProducts = JSON.parse(e.target?.result as string)
        onImportProducts(importedProducts)
        setIsImporting(false)
      } catch (error) {
        console.error("Error importing products:", error)
        alert("Error importing products. Please check the file format.")
        setIsImporting(false)
      }
    }
    reader.readAsText(file)
  }

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-yellow-500/20 mb-6">
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center space-x-4">
          <span className="text-gray-300">
            {selectedProducts.length} of {products.length} selected
          </span>
          {selectedProducts.length > 0 && (
            <div className="flex items-center space-x-2">
              <button
                onClick={onDeleteSelected}
                className="flex items-center space-x-2 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md text-sm transition-colors"
              >
                <Trash2 size={16} />
                <span>Delete Selected</span>
              </button>
              <button
                onClick={onDuplicateSelected}
                className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md text-sm transition-colors"
              >
                <Copy size={16} />
                <span>Duplicate</span>
              </button>
              <button
                onClick={onExportSelected}
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-md text-sm transition-colors"
              >
                <Download size={16} />
                <span>Export</span>
              </button>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <label className="flex items-center space-x-2 bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-md text-sm transition-colors cursor-pointer">
            {isImporting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Upload size={16} />
            )}
            <span>{isImporting ? "Importing..." : "Import JSON"}</span>
            <input type="file" accept=".json" onChange={handleImport} className="hidden" disabled={isImporting} />
          </label>
          <button
            onClick={() => onExportSelected()}
            className="flex items-center space-x-2 bg-yellow-500 hover:bg-yellow-600 text-black px-3 py-2 rounded-md text-sm transition-colors"
          >
            <Download size={16} />
            <span>Export All</span>
          </button>
        </div>
      </div>
    </div>
  )
}
