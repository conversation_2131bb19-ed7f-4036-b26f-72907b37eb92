{"indexes": [{"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "price.egp", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "price.usd", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "isFeatured", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "stockCount", "order": "ASCENDING"}, {"fieldPath": "lowStockThreshold", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "paymentStatus", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "lastLoginAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "totalSpent", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "products", "fieldPath": "name.en", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "products", "fieldPath": "name.ar", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users", "fieldPath": "email", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}