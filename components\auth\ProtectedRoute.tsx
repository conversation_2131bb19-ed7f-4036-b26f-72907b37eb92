"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/lib/types';
import SimpleLoader from '@/components/SimpleLoader';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { ShieldAlert, ArrowLeft } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requiredPermissions?: string[];
  fallbackPath?: string;
  showFallback?: boolean;
}

export default function ProtectedRoute({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  fallbackPath = '/auth',
  showFallback = true
}: ProtectedRouteProps) {
  const { user, userData, loading, hasAnyRole } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

  useEffect(() => {
    if (loading) return;

    // Check if user is authenticated
    if (!user) {
      setIsAuthorized(false);
      if (!showFallback) {
        router.push(fallbackPath);
      }
      return;
    }

    // Check if user data is loaded
    if (!userData) {
      setIsAuthorized(false);
      return;
    }

    // Check if user account is active
    if (!userData.isActive) {
      setIsAuthorized(false);
      return;
    }

    // Check role requirements
    if (requiredRoles.length > 0) {
      const hasRequiredRole = hasAnyRole(requiredRoles);
      if (!hasRequiredRole) {
        setIsAuthorized(false);
        return;
      }
    }

    // Check permission requirements (can be extended based on specific permissions)
    if (requiredPermissions.length > 0) {
      const hasRequiredPermissions = checkPermissions(userData.role, requiredPermissions);
      if (!hasRequiredPermissions) {
        setIsAuthorized(false);
        return;
      }
    }

    setIsAuthorized(true);
  }, [user, userData, loading, requiredRoles, requiredPermissions, hasAnyRole, router, fallbackPath, showFallback]);

  // Helper function to check specific permissions
  const checkPermissions = (userRole: UserRole, permissions: string[]): boolean => {
    const rolePermissions: Record<UserRole, string[]> = {
      [UserRole.CUSTOMER]: ['view_profile', 'place_order', 'view_orders'],
      [UserRole.MODERATOR]: [
        'view_profile', 'place_order', 'view_orders',
        'manage_products', 'view_analytics', 'manage_orders'
      ],
      [UserRole.ADMIN]: [
        'view_profile', 'place_order', 'view_orders',
        'manage_products', 'view_analytics', 'manage_orders',
        'manage_users', 'view_admin_dashboard', 'manage_settings'
      ],
      [UserRole.SUPER_ADMIN]: [
        'view_profile', 'place_order', 'view_orders',
        'manage_products', 'view_analytics', 'manage_orders',
        'manage_users', 'view_admin_dashboard', 'manage_settings',
        'manage_admins', 'system_settings', 'backup_restore'
      ]
    };

    const userPermissions = rolePermissions[userRole] || [];
    return permissions.every(permission => userPermissions.includes(permission));
  };

  // Show loading state
  if (loading || isAuthorized === null) {
    return <SimpleLoader />;
  }

  // Show unauthorized message if not authorized and showFallback is true
  if (!isAuthorized && showFallback) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-6">
          <div className="text-center">
            <ShieldAlert className="mx-auto h-16 w-16 text-red-500 mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
            <p className="text-gray-400 mb-6">
              {!user 
                ? "You need to sign in to access this page."
                : !userData?.isActive
                ? "Your account has been deactivated. Please contact support."
                : "You don't have permission to access this page."
              }
            </p>
          </div>

          <Alert className="border-red-500/20 bg-red-500/10">
            <ShieldAlert className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              {!user 
                ? "Please sign in with an authorized account to continue."
                : requiredRoles.length > 0
                ? `This page requires one of the following roles: ${requiredRoles.join(', ')}`
                : "You don't have the necessary permissions to view this content."
              }
            </AlertDescription>
          </Alert>

          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={() => router.back()}
              variant="outline"
              className="flex-1"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
            
            {!user && (
              <Button
                onClick={() => router.push(fallbackPath)}
                className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black"
              >
                Sign In
              </Button>
            )}
            
            {user && (
              <Button
                onClick={() => router.push('/')}
                className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black"
              >
                Go Home
              </Button>
            )}
          </div>

          {user && userData && (
            <div className="text-center text-sm text-gray-500">
              Signed in as: {userData.displayName || userData.email}
              <br />
              Role: {userData.role}
            </div>
          )}
        </div>
      </div>
    );
  }

  // If not authorized and showFallback is false, don't render anything
  if (!isAuthorized) {
    return null;
  }

  // Render children if authorized
  return <>{children}</>;
}

// Convenience components for common protection scenarios
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRoles={[UserRole.ADMIN, UserRole.SUPER_ADMIN]}>
      {children}
    </ProtectedRoute>
  );
}

export function ModeratorRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRoles={[UserRole.MODERATOR, UserRole.ADMIN, UserRole.SUPER_ADMIN]}>
      {children}
    </ProtectedRoute>
  );
}

export function AuthenticatedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute>
      {children}
    </ProtectedRoute>
  );
}
