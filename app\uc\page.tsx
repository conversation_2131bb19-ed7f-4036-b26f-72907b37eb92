import type { Metadata } from "next"
import ProductCard from "@/components/ProductCard"

export const metadata: Metadata = {
  title: "UC Packages - RNG VIP",
  description: "PUBG UC packages at the best prices. Get your UC instantly and safely.",
}

// Mock data for UC packages
const ucPackages = [
  {
    id: "1",
    name: { en: "60 UC Package", ar: "حزمة 60 يو سي" },
    description: { en: "Small UC package for quick purchases", ar: "حزمة يو سي صغيرة للمشتريات السريعة" },
    price: { egp: 30, usd: 1 },
    image: "/placeholder.svg?height=200&width=300",
    quantity: 60,
    type: "uc" as const,
  },
  {
    id: "2",
    name: { en: "325 UC Package", ar: "حزمة 325 يو سي" },
    description: { en: "Popular UC package for battle pass", ar: "حزمة يو سي شائعة للباتل باس" },
    price: { egp: 150, usd: 5 },
    image: "/placeholder.svg?height=200&width=300",
    quantity: 325,
    type: "uc" as const,
  },
  {
    id: "3",
    name: { en: "660 UC Package", ar: "حزمة 660 يو سي" },
    description: { en: "Great value UC package", ar: "حزمة يو سي بقيمة رائعة" },
    price: { egp: 300, usd: 10 },
    image: "/placeholder.svg?height=200&width=300",
    quantity: 660,
    type: "uc" as const,
  },
  {
    id: "4",
    name: { en: "1800 UC Package", ar: "حزمة 1800 يو سي" },
    description: { en: "Perfect for skins and crates", ar: "مثالية للسكنز والصناديق" },
    price: { egp: 750, usd: 25 },
    image: "/placeholder.svg?height=200&width=300",
    quantity: 1800,
    type: "uc" as const,
  },
  {
    id: "5",
    name: { en: "3500 UC Package", ar: "حزمة 3500 يو سي" },
    description: { en: "Best value UC package", ar: "أفضل قيمة لحزمة يو سي" },
    price: { egp: 1350, usd: 45 },
    image: "/placeholder.svg?height=200&width=300",
    quantity: 3500,
    type: "uc" as const,
  },
  {
    id: "6",
    name: { en: "8100 UC Package", ar: "حزمة 8100 يو سي" },
    description: { en: "Premium UC package for serious players", ar: "حزمة يو سي مميزة للاعبين الجادين" },
    price: { egp: 3000, usd: 100 },
    image: "/placeholder.svg?height=200&width=300",
    quantity: 8100,
    type: "uc" as const,
  },
]

export default function UCPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-yellow-400 mb-4">UC Packages</h1>
        <p className="text-gray-300 text-lg">
          Get your PUBG UC instantly at the best prices. Safe and secure transactions.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {ucPackages.map((uc) => (
          <ProductCard key={uc.id} {...uc} />
        ))}
      </div>
    </div>
  )
}
