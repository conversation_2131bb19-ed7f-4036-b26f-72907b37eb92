"use client"

import { createContext, useContext, useState, useCallback, useMemo, type ReactNode } from "react"

type Language = "en" | "ar"

interface LanguageContextType {
  language: Language
  toggleLanguage: () => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

const translations = {
  en: {
    // Navigation
    home: "Home",
    accounts: "Accounts",
    uc: "UC",
    hacks: "Hacks",
    profile: "Profile",
    admin: "Admin",

    // Home Page
    heroTitle: "RNG VIP Gaming Store",
    heroSubtitle: "Premium PUBG Accounts, UC Packages & Gaming Tools",
    shopNow: "Shop Now",
    featuredAccounts: "Featured Accounts",
    ucPackages: "UC Packages",
    gamingHacks: "Gaming Hacks",
    viewAll: "View All",

    // Product Cards
    price: "Price",
    egp: "EGP",
    usd: "USD",
    quantity: "Quantity",
    category: "Category",
    description: "Description",

    // Footer
    about: "About",
    terms: "Terms",
    contact: "Contact",

    // Admin
    dashboard: "Dashboard",
    addProduct: "Add Product",
    editProduct: "Edit Product",
    deleteProduct: "Delete Product",
    productName: "Product Name",
    productDescription: "Product Description",
    productPrice: "Product Price",
    productImage: "Product Image",
    save: "Save",
    cancel: "Cancel",

    // Profile
    userProfile: "User Profile",
    orderHistory: "Order History",
    accountSettings: "Account Settings",
  },
  ar: {
    // Navigation
    home: "الرئيسية",
    accounts: "الحسابات",
    uc: "يو سي",
    hacks: "الهاكات",
    profile: "الملف الشخصي",
    admin: "الإدارة",

    // Home Page
    heroTitle: "متجر RNG VIP للألعاب",
    heroSubtitle: "حسابات PUBG المميزة وحزم UC وأدوات الألعاب",
    shopNow: "تسوق الآن",
    featuredAccounts: "الحسابات المميزة",
    ucPackages: "حزم UC",
    gamingHacks: "هاكات الألعاب",
    viewAll: "عرض الكل",

    // Product Cards
    price: "السعر",
    egp: "جنيه",
    usd: "دولار",
    quantity: "الكمية",
    category: "الفئة",
    description: "الوصف",

    // Footer
    about: "حول",
    terms: "الشروط",
    contact: "اتصل بنا",

    // Admin
    dashboard: "لوحة التحكم",
    addProduct: "إضافة منتج",
    editProduct: "تعديل منتج",
    deleteProduct: "حذف منتج",
    productName: "اسم المنتج",
    productDescription: "وصف المنتج",
    productPrice: "سعر المنتج",
    productImage: "صورة المنتج",
    save: "حفظ",
    cancel: "إلغاء",

    // Profile
    userProfile: "الملف الشخصي",
    orderHistory: "تاريخ الطلبات",
    accountSettings: "إعدادات الحساب",
  },
}

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>("en")

  const toggleLanguage = useCallback(() => {
    setLanguage((prev) => (prev === "en" ? "ar" : "en"))
  }, [])

  const t = useCallback((key: string): string => {
    return translations[language][key as keyof (typeof translations)["en"]] || key
  }, [language])

  const contextValue = useMemo(() => ({
    language,
    toggleLanguage,
    t
  }), [language, toggleLanguage, t])

  return (
    <LanguageContext.Provider value={contextValue}>
      <div className={language === "ar" ? "rtl" : "ltr"}>{children}</div>
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
