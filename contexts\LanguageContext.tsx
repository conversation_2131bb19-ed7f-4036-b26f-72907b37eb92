"use client"

import React, { createContext, useContext, useState, useCallback, useMemo, useEffect, type ReactNode } from "react"

type Language = "en" | "ar"

interface LanguageContextType {
  language: Language
  toggleLanguage: () => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

const translations = {
  en: {
    // Navigation
    home: "Home",
    accounts: "Accounts",
    uc: "UC",
    hacks: "Hacks",
    profile: "Profile",
    admin: "Admin",

    // Home Page
    heroTitle: "RNG VIP Gaming Store",
    heroSubtitle: "Premium PUBG Accounts, UC Packages & Gaming Tools",
    shopNow: "Shop Now",
    featuredAccounts: "Featured Accounts",
    ucPackages: "UC Packages",
    gamingHacks: "Gaming Hacks",
    viewAll: "View All",

    // Product Cards
    price: "Price",
    egp: "EGP",
    usd: "USD",
    quantity: "Quantity",
    category: "Category",
    description: "Description",

    // Footer
    about: "About",
    terms: "Terms",
    contact: "Contact",

    // Admin
    dashboard: "Dashboard",
    addProduct: "Add Product",
    editProduct: "Edit Product",
    deleteProduct: "Delete Product",
    productName: "Product Name",
    productDescription: "Product Description",
    productPrice: "Product Price",
    productImage: "Product Image",
    save: "Save",
    cancel: "Cancel",

    // Profile
    userProfile: "User Profile",
    orderHistory: "Order History",
    accountSettings: "Account Settings",

    // Auth Page
    welcomeBack: "Welcome Back",
    joinRngStore: "Join RNG Store",
    signInDescription: "Sign in to access your RNG Store account",
    createAccountDescription: "Create your account to access premium gaming content",
    emailAddress: "Email Address",
    password: "Password",
    confirmPassword: "Confirm Password",
    displayName: "Display Name",
    enterEmail: "Enter your email address",
    enterPassword: "Enter your password",
    createStrongPassword: "Create a strong password",
    confirmYourPassword: "Confirm your password",
    chooseDisplayName: "Choose your display name",
    signIn: "Sign In",
    createAccount: "Create Account",
    signingIn: "Signing in...",
    creatingAccount: "Creating account...",
    showPassword: "Show password",
    hidePassword: "Hide password",
    login: "Login",
    signUp: "Sign Up",
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: "Already have an account?",
    loginFailed: "Login Failed",
    registrationFailed: "Registration Failed",
    accountCreated: "Account Created!",
    welcomeBackSuccess: "Welcome back!",
    loginSuccessMessage: "You've been logged in successfully.",
    emailVerificationMessage: "Please check your email to verify your account before signing in.",
    ultimateGamingStore: "Welcome to the ultimate gaming store",

    // Form Validation
    validEmailRequired: "Please enter a valid email address",
    passwordMinLength: "Password must be at least 6 characters",
    displayNameMinLength: "Display name must be at least 2 characters",
    passwordsDontMatch: "Passwords don't match",
  },
  ar: {
    // Navigation
    home: "الرئيسية",
    accounts: "الحسابات",
    uc: "يو سي",
    hacks: "الهاكات",
    profile: "الملف الشخصي",
    admin: "الإدارة",

    // Home Page
    heroTitle: "متجر RNG VIP للألعاب",
    heroSubtitle: "حسابات PUBG المميزة وحزم UC وأدوات الألعاب",
    shopNow: "تسوق الآن",
    featuredAccounts: "الحسابات المميزة",
    ucPackages: "حزم UC",
    gamingHacks: "هاكات الألعاب",
    viewAll: "عرض الكل",

    // Product Cards
    price: "السعر",
    egp: "جنيه",
    usd: "دولار",
    quantity: "الكمية",
    category: "الفئة",
    description: "الوصف",

    // Footer
    about: "حول",
    terms: "الشروط",
    contact: "اتصل بنا",

    // Admin
    dashboard: "لوحة التحكم",
    addProduct: "إضافة منتج",
    editProduct: "تعديل منتج",
    deleteProduct: "حذف منتج",
    productName: "اسم المنتج",
    productDescription: "وصف المنتج",
    productPrice: "سعر المنتج",
    productImage: "صورة المنتج",
    save: "حفظ",
    cancel: "إلغاء",

    // Profile
    userProfile: "الملف الشخصي",
    orderHistory: "تاريخ الطلبات",
    accountSettings: "إعدادات الحساب",

    // Auth Page
    welcomeBack: "مرحباً بعودتك",
    joinRngStore: "انضم إلى RNG Store",
    signInDescription: "سجل دخولك للوصول إلى حسابك في RNG Store",
    createAccountDescription: "أنشئ حسابك للوصول إلى المحتوى المميز للألعاب",
    emailAddress: "عنوان البريد الإلكتروني",
    password: "كلمة المرور",
    confirmPassword: "تأكيد كلمة المرور",
    displayName: "الاسم المعروض",
    enterEmail: "أدخل عنوان بريدك الإلكتروني",
    enterPassword: "أدخل كلمة المرور",
    createStrongPassword: "أنشئ كلمة مرور قوية",
    confirmYourPassword: "أكد كلمة المرور",
    chooseDisplayName: "اختر اسمك المعروض",
    signIn: "تسجيل الدخول",
    createAccount: "إنشاء حساب",
    signingIn: "جاري تسجيل الدخول...",
    creatingAccount: "جاري إنشاء الحساب...",
    showPassword: "إظهار كلمة المرور",
    hidePassword: "إخفاء كلمة المرور",
    login: "تسجيل الدخول",
    signUp: "إنشاء حساب",
    dontHaveAccount: "ليس لديك حساب؟",
    alreadyHaveAccount: "لديك حساب بالفعل؟",
    loginFailed: "فشل تسجيل الدخول",
    registrationFailed: "فشل إنشاء الحساب",
    accountCreated: "تم إنشاء الحساب!",
    welcomeBackSuccess: "مرحباً بعودتك!",
    loginSuccessMessage: "تم تسجيل دخولك بنجاح.",
    emailVerificationMessage: "يرجى التحقق من بريدك الإلكتروني لتأكيد حسابك قبل تسجيل الدخول.",
    ultimateGamingStore: "مرحباً بك في متجر الألعاب الأفضل",

    // Form Validation
    validEmailRequired: "يرجى إدخال عنوان بريد إلكتروني صحيح",
    passwordMinLength: "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
    displayNameMinLength: "يجب أن يكون الاسم المعروض حرفين على الأقل",
    passwordsDontMatch: "كلمات المرور غير متطابقة",
  },
}

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>("en")

  const toggleLanguage = useCallback(() => {
    setLanguage((prev) => {
      const newLang = prev === "en" ? "ar" : "en"
      // Update document language only (not direction globally)
      if (typeof document !== 'undefined') {
        document.documentElement.lang = newLang
      }
      return newLang
    })
  }, [])

  const t = useCallback((key: string): string => {
    return translations[language][key as keyof (typeof translations)["en"]] || key
  }, [language])

  const contextValue = useMemo(() => ({
    language,
    toggleLanguage,
    t
  }), [language, toggleLanguage, t])

  // Set initial language on mount
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.lang = language
    }
  }, [language])

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
