"use client"

import Link from "next/link"
import { useLanguage } from "@/contexts/LanguageContext"

export default function Footer() {
  const { t } = useLanguage()

  return (
    <footer className="bg-black border-t border-yellow-500/20 mt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo & Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                <span className="text-black font-bold text-sm">RNG</span>
              </div>
              <span className="text-xl font-bold text-yellow-400">VIP</span>
            </div>
            <p className="text-gray-400 max-w-md">
              Your ultimate destination for premium PUBG accounts, UC packages, and gaming tools.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-yellow-400 font-semibold mb-4">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/accounts" className="block text-gray-400 hover:text-yellow-400 transition-colors">
                {t("accounts")}
              </Link>
              <Link href="/uc" className="block text-gray-400 hover:text-yellow-400 transition-colors">
                {t("uc")}
              </Link>
              <Link href="/hacks" className="block text-gray-400 hover:text-yellow-400 transition-colors">
                {t("hacks")}
              </Link>
            </div>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-yellow-400 font-semibold mb-4">Support</h3>
            <div className="space-y-2">
              <Link href="/about" className="block text-gray-400 hover:text-yellow-400 transition-colors">
                {t("about")}
              </Link>
              <Link href="/terms" className="block text-gray-400 hover:text-yellow-400 transition-colors">
                {t("terms")}
              </Link>
              <Link href="/contact" className="block text-gray-400 hover:text-yellow-400 transition-colors">
                {t("contact")}
              </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-yellow-500/20 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 RNG VIP. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
