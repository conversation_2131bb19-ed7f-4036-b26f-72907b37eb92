import type { Metadata } from "next"
import { notFound } from "next/navigation"
import ImageGallery from "@/components/ImageGallery"
import AddToCartButton from "@/components/AddToCartButton"
import { Shield, Clock, CreditCard, Gift, Star } from "lucide-react"

// Mock data for UC packages (all demo items so /uc/1-6 resolve)
const ucData = {
  "1": {
    id: "1",
    name: { en: "60 UC Package", ar: "حزمة 60 يو سي" },
    description: {
      en: "Small UC package perfect for quick purchases like room cards and small items. Instant delivery guaranteed.",
      ar: "حزمة يو سي صغيرة مثالية للمشتريات السريعة مثل كروت الغرف والعناصر الصغيرة. تسليم فوري مضمون.",
    },
    price: { egp: 30, usd: 1 },
    quantity: 60,
    images: ["/placeholder.svg?height=400&width=600", "/placeholder.svg?height=400&width=600"],
    deliveryTime: "Instant",
    features: {
      en: [
        "Instant delivery to your account",
        "100% safe and secure transaction",
        "24/7 customer support",
        "Perfect for room cards",
        "Small item purchases",
        "No account sharing required",
      ],
      ar: [
        "تسليم فوري لحسابك",
        "معاملة آمنة 100%",
        "دعم عملاء 24/7",
        "مثالي لكروت الغرف",
        "مشتريات العناصر الصغيرة",
        "لا يتطلب مشاركة الحساب",
      ],
    },
    whatYouCanBuy: {
      en: ["6 Room Cards (10 UC each)", "Name Change Card (60 UC)", "Small emotes and stickers", "Basic avatar frames"],
      ar: [
        "6 كروت غرف (10 يو سي لكل كارت)",
        "كارت تغيير الاسم (60 يو سي)",
        "إيموجي وملصقات صغيرة",
        "إطارات أفاتار أساسية",
      ],
    },
    bonus: null,
  },

  "2": {
    id: "2",
    name: { en: "325 UC Package", ar: "حزمة 325 يو سي" },
    description: {
      en: "Popular UC package perfect for Royal Pass purchase and premium crates. Most popular choice among players.",
      ar: "حزمة يو سي شائعة مثالية لشراء الرويال باس والصناديق المميزة. الخيار الأكثر شعبية بين اللاعبين.",
    },
    price: { egp: 150, usd: 5 },
    quantity: 325,
    images: ["/placeholder.svg?height=400&width=600", "/placeholder.svg?height=400&width=600"],
    deliveryTime: "Instant",
    features: {
      en: [
        "Instant delivery to your account",
        "100% safe and secure transaction",
        "24/7 customer support",
        "Perfect for Royal Pass purchase",
        "Great for premium crates",
        "Most popular package",
      ],
      ar: [
        "تسليم فوري لحسابك",
        "معاملة آمنة 100%",
        "دعم عملاء 24/7",
        "مثالي لشراء الرويال باس",
        "رائع للصناديق المميزة",
        "الحزمة الأكثر شعبية",
      ],
    },
    whatYouCanBuy: {
      en: [
        "Royal Pass (300 UC) + extras",
        "32 Premium Crates (10 UC each)",
        "Multiple name changes",
        "Premium avatar frames",
        "Emote collections",
      ],
      ar: [
        "الرويال باس (300 يو سي) + إضافات",
        "32 صندوق مميز (10 يو سي لكل صندوق)",
        "تغييرات أسماء متعددة",
        "إطارات أفاتار مميزة",
        "مجموعات الإيموجي",
      ],
    },
    bonus: { en: "25 UC Bonus", ar: "مكافأة 25 يو سي" },
  },

  "3": {
    id: "3",
    name: { en: "660 UC Package", ar: "حزمة 660 يو سي" },
    description: {
      en: "Great value UC package perfect for multiple Royal Pass seasons and premium purchases. Excellent value for money.",
      ar: "حزمة يو سي بقيمة رائعة مثالية لمواسم رويال باس متعددة والمشتريات المميزة. قيمة ممتازة مقابل المال.",
    },
    price: { egp: 300, usd: 10 },
    quantity: 660,
    images: ["/placeholder.svg?height=400&width=600", "/placeholder.svg?height=400&width=600"],
    deliveryTime: "Instant",
    features: {
      en: [
        "Instant delivery to your account",
        "100% safe and secure transaction",
        "24/7 customer support",
        "Great value for money",
        "Perfect for multiple purchases",
        "Bonus UC included",
      ],
      ar: [
        "تسليم فوري لحسابك",
        "معاملة آمنة 100%",
        "دعم عملاء 24/7",
        "قيمة رائعة مقابل المال",
        "مثالي للمشتريات المتعددة",
        "يو سي إضافي مشمول",
      ],
    },
    whatYouCanBuy: {
      en: [
        "2 Royal Pass seasons (600 UC)",
        "66 Premium Crates (10 UC each)",
        "Legendary outfit pieces",
        "Premium weapon finishes",
        "Multiple upgrades",
      ],
      ar: [
        "موسمين رويال باس (600 يو سي)",
        "66 صندوق مميز (10 يو سي لكل صندوق)",
        "قطع ملابس أسطورية",
        "تشطيبات أسلحة مميزة",
        "ترقيات متعددة",
      ],
    },
    bonus: { en: "60 UC Bonus", ar: "مكافأة 60 يو سي" },
  },

  "4": {
    id: "4",
    name: { en: "1800 UC Package", ar: "حزمة 1800 يو سي" },
    description: {
      en: "Perfect UC package for serious players. Ideal for skins, crates, and multiple Royal Pass seasons with great bonuses.",
      ar: "حزمة يو سي مثالية للاعبين الجادين. مثالية للسكنز والصناديق ومواسم رويال باس متعددة مع مكافآت رائعة.",
    },
    price: { egp: 750, usd: 25 },
    quantity: 1800,
    images: ["/placeholder.svg?height=400&width=600", "/placeholder.svg?height=400&width=600"],
    deliveryTime: "Instant",
    features: {
      en: [
        "Instant delivery to your account",
        "100% safe and secure transaction",
        "24/7 customer support",
        "Perfect for serious players",
        "Great for legendary items",
        "Substantial bonus UC",
      ],
      ar: [
        "تسليم فوري لحسابك",
        "معاملة آمنة 100%",
        "دعم عملاء 24/7",
        "مثالي للاعبين الجادين",
        "رائع للعناصر الأسطورية",
        "مكافأة يو سي كبيرة",
      ],
    },
    whatYouCanBuy: {
      en: [
        "6 Royal Pass seasons (1800 UC)",
        "180 Premium Crates (10 UC each)",
        "Multiple legendary outfits",
        "Weapon skin collections",
        "Vehicle customizations",
        "Premium upgrades",
      ],
      ar: [
        "6 مواسم رويال باس (1800 يو سي)",
        "180 صندوق مميز (10 يو سي لكل صندوق)",
        "ملابس أسطورية متعددة",
        "مجموعات سكنز الأسلحة",
        "تخصيصات المركبات",
        "ترقيات مميزة",
      ],
    },
    bonus: { en: "200 UC Bonus", ar: "مكافأة 200 يو سي" },
  },

  "5": {
    id: "5",
    name: { en: "3500 UC Package", ar: "حزمة 3500 يو سي" },
    description: {
      en: "Best value UC package for dedicated players. Perfect for collecting rare items and multiple premium purchases.",
      ar: "أفضل قيمة لحزمة يو سي للاعبين المتفانين. مثالية لجمع العناصر النادرة والمشتريات المميزة المتعددة.",
    },
    price: { egp: 1350, usd: 45 },
    quantity: 3500,
    images: ["/placeholder.svg?height=400&width=600", "/placeholder.svg?height=400&width=600"],
    deliveryTime: "Instant",
    features: {
      en: [
        "Instant delivery to your account",
        "100% safe and secure transaction",
        "24/7 customer support",
        "Best value package",
        "Perfect for collectors",
        "Maximum bonus UC",
      ],
      ar: [
        "تسليم فوري لحسابك",
        "معاملة آمنة 100%",
        "دعم عملاء 24/7",
        "أفضل قيمة للحزمة",
        "مثالي للجامعين",
        "أقصى مكافأة يو سي",
      ],
    },
    whatYouCanBuy: {
      en: [
        "11+ Royal Pass seasons",
        "350+ Premium Crates",
        "Complete outfit collections",
        "Mythic weapon skins",
        "Legendary vehicle skins",
        "All premium upgrades",
      ],
      ar: [
        "11+ موسم رويال باس",
        "350+ صندوق مميز",
        "مجموعات ملابس كاملة",
        "سكنز أسلحة خرافية",
        "سكنز مركبات أسطورية",
        "جميع الترقيات المميزة",
      ],
    },
    bonus: { en: "500 UC Bonus", ar: "مكافأة 500 يو سي" },
  },

  "6": {
    id: "6",
    name: { en: "8100 UC Package", ar: "حزمة 8100 يو سي" },
    description: {
      en: "Premium UC package for the most dedicated players. Ultimate package for collectors and competitive players.",
      ar: "حزمة يو سي مميزة للاعبين الأكثر تفانياً. الحزمة النهائية للجامعين واللاعبين التنافسيين.",
    },
    price: { egp: 3000, usd: 100 },
    quantity: 8100,
    images: ["/placeholder.svg?height=400&width=600", "/placeholder.svg?height=400&width=600"],
    deliveryTime: "Instant",
    features: {
      en: [
        "Instant delivery to your account",
        "100% safe and secure transaction",
        "24/7 premium customer support",
        "Ultimate premium package",
        "Perfect for serious collectors",
        "Massive bonus UC included",
      ],
      ar: [
        "تسليم فوري لحسابك",
        "معاملة آمنة 100%",
        "دعم عملاء مميز 24/7",
        "الحزمة المميزة النهائية",
        "مثالي للجامعين الجادين",
        "مكافأة يو سي ضخمة مشمولة",
      ],
    },
    whatYouCanBuy: {
      en: [
        "27 Royal Pass seasons",
        "810+ Premium Crates",
        "All available outfits",
        "Complete weapon collections",
        "All vehicle customizations",
        "Every premium feature",
      ],
      ar: [
        "27 موسم رويال باس",
        "810+ صندوق مميز",
        "جميع الملابس المتاحة",
        "مجموعات أسلحة كاملة",
        "جميع تخصيصات المركبات",
        "كل ميزة مميزة",
      ],
    },
    bonus: { en: "1200 UC Bonus", ar: "مكافأة 1200 يو سي" },
  },
} satisfies Record<string, any>

interface PageProps {
  params: { id: string }
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const uc = ucData[params.id as keyof typeof ucData]

  if (!uc) {
    return {
      title: "UC Package Not Found - RNG VIP",
    }
  }

  return {
    title: `${uc.name.en} - RNG VIP`,
    description: uc.description.en,
  }
}

export default function UCDetailPage({ params }: PageProps) {
  const uc = ucData[params.id as keyof typeof ucData]

  if (!uc) {
    notFound()
  }

  const getPackageColor = (quantity: number) => {
    if (quantity >= 3500) return "from-purple-500 to-pink-500"
    if (quantity >= 1800) return "from-yellow-500 to-orange-500"
    if (quantity >= 660) return "from-blue-500 to-cyan-500"
    if (quantity >= 325) return "from-green-500 to-emerald-500"
    return "from-gray-500 to-gray-600"
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Gallery */}
        <div>
          <ImageGallery images={uc.images} productName={uc.name.en} />
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <div className="flex items-center space-x-4 mb-4">
              <h1 className="text-3xl font-bold text-yellow-400">{uc.name.en}</h1>
              <div
                className={`bg-gradient-to-r ${getPackageColor(uc.quantity)} text-white px-4 py-2 rounded-full font-bold text-lg shadow-lg`}
              >
                {uc.quantity} UC
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-gray-300 mb-4">{uc.name.ar}</h2>
            <p className="text-gray-300 leading-relaxed">{uc.description.en}</p>
            <p className="text-gray-400 leading-relaxed mt-2">{uc.description.ar}</p>
          </div>

          {/* Package Highlights */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="bg-gray-800 rounded-lg p-4 text-center border border-green-500/20">
              <Clock className="text-green-400 mx-auto mb-2" size={24} />
              <div className="text-sm font-medium text-green-400">Instant</div>
              <div className="text-xs text-gray-400">Delivery</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center border border-blue-500/20">
              <Shield className="text-blue-400 mx-auto mb-2" size={24} />
              <div className="text-sm font-medium text-blue-400">100% Safe</div>
              <div className="text-xs text-gray-400">Transaction</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center border border-purple-500/20">
              <CreditCard className="text-purple-400 mx-auto mb-2" size={24} />
              <div className="text-sm font-medium text-purple-400">24/7</div>
              <div className="text-xs text-gray-400">Support</div>
            </div>
          </div>

          {/* Bonus Badge */}
          {uc.bonus && (
            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Gift className="text-yellow-400" size={24} />
                <div>
                  <h3 className="text-yellow-400 font-semibold">Special Bonus!</h3>
                  <p className="text-gray-300 text-sm">
                    Get extra {uc.bonus.en} with this package - Limited time offer!
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Features */}
          <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
            <h3 className="text-xl font-bold text-yellow-400 mb-4">Package Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-white mb-2">English</h4>
                <ul className="space-y-1">
                  {uc.features.en.map((feature, index) => (
                    <li key={index} className="text-gray-300 text-sm flex items-start">
                      <span className="text-yellow-400 mr-2">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">العربية</h4>
                <ul className="space-y-1">
                  {uc.features.ar.map((feature, index) => (
                    <li key={index} className="text-gray-300 text-sm flex items-start">
                      <span className="text-yellow-400 mr-2">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Add to Cart */}
          <AddToCartButton productId={uc.id} productType="uc" price={uc.price} />
        </div>
      </div>

      {/* What You Can Buy */}
      <div className="mt-12">
        <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
          <div className="flex items-center space-x-3 mb-6">
            <Star className="text-yellow-400" size={24} />
            <h3 className="text-2xl font-bold text-yellow-400">What You Can Buy with {uc.quantity} UC</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-white mb-3 flex items-center">
                <span className="mr-2">🇺🇸</span> English
              </h4>
              <div className="space-y-3">
                {uc.whatYouCanBuy.en.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300">{item}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-3 flex items-center">
                <span className="mr-2">🇸🇦</span> العربية
              </h4>
              <div className="space-y-3">
                {uc.whatYouCanBuy.ar.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-300">{item}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Value Comparison */}
      <div className="mt-8">
        <div className="bg-gradient-to-r from-green-900/20 to-blue-900/20 border border-green-500/30 rounded-lg p-6">
          <h3 className="text-xl font-bold text-green-400 mb-4">💰 Value Breakdown</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{uc.quantity}</div>
              <div className="text-sm text-gray-400">Base UC</div>
            </div>
            {uc.bonus && (
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">+{uc.bonus.en.match(/\d+/)?.[0]}</div>
                <div className="text-sm text-gray-400">Bonus UC</div>
              </div>
            )}
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {uc.bonus ? uc.quantity + Number.parseInt(uc.bonus.en.match(/\d+/)?.[0] || "0") : uc.quantity}
              </div>
              <div className="text-sm text-gray-400">Total UC</div>
            </div>
          </div>
        </div>
      </div>

      {/* Purchase Instructions */}
      <div className="mt-8">
        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
          <h3 className="text-xl font-bold text-blue-400 mb-4">📱 How to Purchase</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-white mb-3">English Instructions</h4>
              <ol className="space-y-2 text-gray-300 text-sm">
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    1
                  </span>
                  Click "Add to Cart" and proceed to checkout
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    2
                  </span>
                  Provide your PUBG Mobile Player ID
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    3
                  </span>
                  Complete payment using your preferred method
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    4
                  </span>
                  UC will be delivered instantly to your account
                </li>
              </ol>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-3">تعليمات باللغة العربية</h4>
              <ol className="space-y-2 text-gray-300 text-sm">
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    1
                  </span>
                  اضغط على "إضافة للسلة" وانتقل للدفع
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    2
                  </span>
                  قدم معرف لاعب PUBG Mobile الخاص بك
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    3
                  </span>
                  أكمل الدفع باستخدام الطريقة المفضلة لديك
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
                    4
                  </span>
                  سيتم تسليم اليو سي فوراً لحسابك
                </li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
