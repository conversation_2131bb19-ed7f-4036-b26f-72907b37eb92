"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { DatabaseService } from '@/lib/database';
import { Product, ProductType } from '@/lib/types';
import { formatPrice, getProductTypeLabel, isProductLowStock, isProductInStock } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { 
  Package, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown, 
  Plus, 
  Minus,
  RefreshCw,
  Download,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Edit
} from 'lucide-react';
import { toast } from 'sonner';

interface InventoryStats {
  totalProducts: number;
  inStockProducts: number;
  lowStockProducts: number;
  outOfStockProducts: number;
  totalValue: { egp: number; usd: number };
}

export default function InventoryManager() {
  const { canManageProducts } = useAuth();
  
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'low-stock' | 'out-of-stock' | 'in-stock'>('all');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [stockUpdateDialog, setStockUpdateDialog] = useState(false);
  const [newStockCount, setNewStockCount] = useState(0);

  // Load inventory data
  const loadInventory = useCallback(async () => {
    if (!canManageProducts) return;
    
    setLoading(true);
    try {
      const response = await DatabaseService.getProducts({}, 1, 1000); // Get all products
      setProducts(response.data);
      calculateStats(response.data);
    } catch (error) {
      console.error('Error loading inventory:', error);
      toast.error('Failed to load inventory');
    } finally {
      setLoading(false);
    }
  }, [canManageProducts]);

  useEffect(() => {
    loadInventory();
  }, [loadInventory]);

  // Calculate inventory statistics
  const calculateStats = (productList: Product[]) => {
    const stats: InventoryStats = {
      totalProducts: productList.length,
      inStockProducts: productList.filter(p => isProductInStock(p)).length,
      lowStockProducts: productList.filter(p => isProductLowStock(p)).length,
      outOfStockProducts: productList.filter(p => p.stockCount === 0).length,
      totalValue: productList.reduce(
        (total, product) => ({
          egp: total.egp + (product.price.egp * product.stockCount),
          usd: total.usd + (product.price.usd * product.stockCount)
        }),
        { egp: 0, usd: 0 }
      )
    };
    setStats(stats);
  };

  // Filter products
  useEffect(() => {
    let filtered = products;

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.en.toLowerCase().includes(term) ||
        product.name.ar.toLowerCase().includes(term)
      );
    }

    // Apply stock filter
    switch (filterType) {
      case 'low-stock':
        filtered = filtered.filter(p => isProductLowStock(p));
        break;
      case 'out-of-stock':
        filtered = filtered.filter(p => p.stockCount === 0);
        break;
      case 'in-stock':
        filtered = filtered.filter(p => isProductInStock(p));
        break;
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, filterType]);

  // Update stock count
  const handleUpdateStock = async (product: Product, newCount: number) => {
    try {
      await DatabaseService.updateProduct(product.id, {
        stockCount: newCount,
        updatedBy: 'current-user-id'
      });
      
      await loadInventory();
      toast.success('Stock updated successfully');
      setStockUpdateDialog(false);
      setSelectedProduct(null);
    } catch (error) {
      console.error('Error updating stock:', error);
      toast.error('Failed to update stock');
    }
  };

  // Quick stock adjustment
  const handleQuickStockAdjustment = async (product: Product, adjustment: number) => {
    const newCount = Math.max(0, product.stockCount + adjustment);
    await handleUpdateStock(product, newCount);
  };

  // Export inventory report
  const handleExportReport = () => {
    const reportData = filteredProducts.map(product => ({
      id: product.id,
      name: product.name.en,
      type: getProductTypeLabel(product.type),
      stockCount: product.stockCount,
      lowStockThreshold: product.lowStockThreshold,
      priceEGP: product.price.egp,
      priceUSD: product.price.usd,
      totalValueEGP: product.price.egp * product.stockCount,
      totalValueUSD: product.price.usd * product.stockCount,
      status: product.stockCount === 0 ? 'Out of Stock' : 
              isProductLowStock(product) ? 'Low Stock' : 'In Stock',
      isActive: product.isActive
    }));

    const dataStr = JSON.stringify(reportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `inventory-report-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
    
    toast.success('Inventory report exported successfully');
  };

  if (!canManageProducts) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You don't have permission to manage inventory.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-yellow-400">Inventory Management</h1>
          <p className="text-gray-400">Track and manage your product stock levels</p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={loadInventory}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            onClick={handleExportReport}
            variant="outline"
          >
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Total Products</CardTitle>
              <Package className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.totalProducts}</div>
              <Progress 
                value={100} 
                className="mt-2 h-2"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">In Stock</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.inStockProducts}</div>
              <Progress 
                value={(stats.inStockProducts / stats.totalProducts) * 100} 
                className="mt-2 h-2"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Low Stock</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.lowStockProducts}</div>
              <Progress 
                value={(stats.lowStockProducts / stats.totalProducts) * 100} 
                className="mt-2 h-2"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Out of Stock</CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.outOfStockProducts}</div>
              <Progress 
                value={(stats.outOfStockProducts / stats.totalProducts) * 100} 
                className="mt-2 h-2"
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Total Inventory Value */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-green-500" />
              Total Inventory Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="text-center p-4 border border-gray-700 rounded-lg">
                <p className="text-sm text-gray-400 mb-1">EGP Value</p>
                <p className="text-2xl font-bold text-yellow-400">
                  {formatPrice(stats.totalValue, 'egp')}
                </p>
              </div>
              <div className="text-center p-4 border border-gray-700 rounded-lg">
                <p className="text-sm text-gray-400 mb-1">USD Value</p>
                <p className="text-2xl font-bold text-green-400">
                  {formatPrice(stats.totalValue, 'usd')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerts */}
      {stats && stats.lowStockProducts > 0 && (
        <Alert className="border-yellow-500/20 bg-yellow-500/10">
          <AlertTriangle className="h-4 w-4 text-yellow-500" />
          <AlertDescription className="text-yellow-200">
            {stats.lowStockProducts} products are running low on stock and need restocking.
          </AlertDescription>
        </Alert>
      )}

      {stats && stats.outOfStockProducts > 0 && (
        <Alert className="border-red-500/20 bg-red-500/10">
          <XCircle className="h-4 w-4 text-red-500" />
          <AlertDescription className="text-red-200">
            {stats.outOfStockProducts} products are out of stock and unavailable for purchase.
          </AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant={filterType === 'all' ? 'default' : 'outline'}
                onClick={() => setFilterType('all')}
                size="sm"
              >
                All
              </Button>
              <Button
                variant={filterType === 'in-stock' ? 'default' : 'outline'}
                onClick={() => setFilterType('in-stock')}
                size="sm"
              >
                In Stock
              </Button>
              <Button
                variant={filterType === 'low-stock' ? 'default' : 'outline'}
                onClick={() => setFilterType('low-stock')}
                size="sm"
              >
                Low Stock
              </Button>
              <Button
                variant={filterType === 'out-of-stock' ? 'default' : 'outline'}
                onClick={() => setFilterType('out-of-stock')}
                size="sm"
              >
                Out of Stock
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products List */}
      <Card>
        <CardHeader>
          <CardTitle>
            Products ({filteredProducts.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto"></div>
              <p className="mt-2 text-gray-400">Loading inventory...</p>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No products found</h3>
              <p className="text-gray-400">
                {searchTerm ? 'No products match your search criteria.' : 'No products available.'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredProducts.map((product) => (
                <div key={product.id} className="flex items-center space-x-4 p-4 border border-gray-700 rounded-lg">
                  <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                    <Package className="h-6 w-6 text-gray-400" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-lg font-semibold text-white truncate">
                        {product.name.en}
                      </h3>
                      
                      <Badge variant="secondary">
                        {getProductTypeLabel(product.type)}
                      </Badge>
                      
                      {product.stockCount === 0 ? (
                        <Badge variant="destructive">Out of Stock</Badge>
                      ) : isProductLowStock(product) ? (
                        <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400">
                          Low Stock
                        </Badge>
                      ) : (
                        <Badge variant="default" className="bg-green-500/20 text-green-400">
                          In Stock
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>Stock: {product.stockCount}</span>
                      <span>Threshold: {product.lowStockThreshold}</span>
                      <span>{formatPrice(product.price, 'egp')}</span>
                      <span>{formatPrice(product.price, 'usd')}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleQuickStockAdjustment(product, -1)}
                      disabled={product.stockCount === 0}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    
                    <span className="text-lg font-semibold text-white min-w-[3rem] text-center">
                      {product.stockCount}
                    </span>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleQuickStockAdjustment(product, 1)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                    
                    <Dialog open={stockUpdateDialog && selectedProduct?.id === product.id} onOpenChange={setStockUpdateDialog}>
                      <DialogTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedProduct(product);
                            setNewStockCount(product.stockCount);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Update Stock - {product.name.en}</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="stock-count">New Stock Count</Label>
                            <Input
                              id="stock-count"
                              type="number"
                              value={newStockCount}
                              onChange={(e) => setNewStockCount(parseInt(e.target.value) || 0)}
                              min="0"
                            />
                          </div>
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              onClick={() => setStockUpdateDialog(false)}
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={() => handleUpdateStock(product, newStockCount)}
                              className="bg-yellow-500 hover:bg-yellow-600 text-black"
                            >
                              Update Stock
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stock Update Dialog */}
      {selectedProduct && (
        <Dialog open={stockUpdateDialog} onOpenChange={setStockUpdateDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Update Stock - {selectedProduct.name.en}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="stock-count">New Stock Count</Label>
                <Input
                  id="stock-count"
                  type="number"
                  value={newStockCount}
                  onChange={(e) => setNewStockCount(parseInt(e.target.value) || 0)}
                  min="0"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setStockUpdateDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => handleUpdateStock(selectedProduct, newStockCount)}
                  className="bg-yellow-500 hover:bg-yellow-600 text-black"
                >
                  Update Stock
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
