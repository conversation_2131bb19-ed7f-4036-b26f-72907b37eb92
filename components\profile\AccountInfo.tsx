"use client"

import { useState, useEffect } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { useAuth } from "@/contexts/AuthContext"
import { Edit, Save, X, User, Mail } from "lucide-react"

export default function AccountInfo() {
  const { t, language } = useLanguage()
  const { userData, user, updateUserData } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    displayName: "",
    email: "",
  })

  const [editData, setEditData] = useState(formData)

  // Update form data when user data changes
  useEffect(() => {
    if (userData && user) {
      const newData = {
        displayName: userData.displayName || user.email?.split('@')[0] || "",
        email: user.email || "",
      }
      setFormData(newData)
      setEditData(newData)
    }
  }, [userData, user])

  const handleSave = async () => {
    try {
      await updateUserData({
        displayName: editData.displayName,
      })
      setFormData(editData)
      setIsEditing(false)
    } catch (error) {
      console.error("Error updating user data:", error)
    }
  }

  const handleCancel = () => {
    setEditData(formData)
    setIsEditing(false)
  }

  return (
    <div className={`space-y-6 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Basic Account Information */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-yellow-400">{t("userProfile")}</h3>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              <Edit size={16} />
              <span>Edit</span>
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
              >
                <Save size={16} />
                <span>{t("save")}</span>
              </button>
              <button
                onClick={handleCancel}
                className="flex items-center space-x-2 bg-gray-500 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                <X size={16} />
                <span>{t("cancel")}</span>
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t("displayName")}
            </label>
            {isEditing ? (
              <input
                type="text"
                value={editData.displayName}
                onChange={(e) => setEditData({ ...editData, displayName: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <User size={16} className="text-gray-400" />
                <span className="text-white">{formData.displayName || "Not set"}</span>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t("emailAddress")}
            </label>
            <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
              <Mail size={16} className="text-gray-400" />
              <span className="text-white">{formData.email}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">Email cannot be changed. Contact support if needed.</p>
          </div>
        </div>
      </div>

      {/* Account Status */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <h3 className="text-xl font-bold text-yellow-400 mb-4">Account Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-green-400">Active</div>
            <div className="text-sm text-gray-400">Account Status</div>
          </div>
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-blue-400">
              {userData?.isEmailVerified ? "Verified" : "Pending"}
            </div>
            <div className="text-sm text-gray-400">Email Status</div>
          </div>
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-purple-400">
              {userData?.role || "Customer"}
            </div>
            <div className="text-sm text-gray-400">Account Type</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <h3 className="text-xl font-bold text-yellow-400 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-left">
            <div className="text-white font-medium">View Order History</div>
            <div className="text-gray-400 text-sm">Check your recent purchases</div>
          </button>
          <button className="p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-left">
            <div className="text-white font-medium">Account Settings</div>
            <div className="text-gray-400 text-sm">Manage your preferences</div>
          </button>
        </div>
      </div>
    </div>
  )
}