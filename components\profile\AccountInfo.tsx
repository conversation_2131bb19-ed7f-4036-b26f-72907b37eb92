"use client"

import { useState, useEffect } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { useAuth } from "@/contexts/AuthContext"
import { Edit, Save, X, User, Mail } from "lucide-react"

export default function AccountInfo() {
  const { t, language } = useLanguage()
  const { userData, user, updateUserData } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const [formData, setFormData] = useState({
    displayName: "",
    email: "",
    firstName: "",
    lastName: "",
    phone: "",
  })

  const [editData, setEditData] = useState(formData)

  // Update form data when user data changes
  useEffect(() => {
    if (userData && user) {
      const newData = {
        displayName: userData.displayName || user.email?.split('@')[0] || "",
        email: userData.email || user.email || "",
        firstName: userData.firstName || "",
        lastName: userData.lastName || "",
        phone: userData.phone || "",
      }
      setFormData(newData)
      setEditData(newData)
    }
  }, [userData, user])

  const handleSave = async () => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(null)

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(editData.email)) {
        setError("Please enter a valid email address")
        return
      }

      // Validate display name
      if (!editData.displayName.trim()) {
        setError("Display name is required")
        return
      }

      // Update user data in database
      await updateUserData({
        displayName: editData.displayName.trim(),
        email: editData.email.trim(),
        firstName: editData.firstName.trim(),
        lastName: editData.lastName.trim(),
        phone: editData.phone.trim(),
      })

      setFormData(editData)
      setIsEditing(false)
      setSuccess("Profile updated successfully!")

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000)

    } catch (error) {
      console.error("Error updating user data:", error)
      setError(error instanceof Error ? error.message : "Failed to update profile")
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setEditData(formData)
    setIsEditing(false)
  }

  return (
    <div className={`space-y-6 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Basic Account Information */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-yellow-400">Profile Information</h3>
          {!isEditing ? (
            <button
              onClick={() => {
                setIsEditing(true)
                setError(null)
                setSuccess(null)
              }}
              className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              <Edit size={16} />
              <span>Edit Profile</span>
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                disabled={loading}
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 disabled:bg-green-400 text-white px-4 py-2 rounded-md transition-colors"
              >
                <Save size={16} />
                <span>{loading ? "Saving..." : "Save"}</span>
              </button>
              <button
                onClick={handleCancel}
                disabled={loading}
                className="flex items-center space-x-2 bg-gray-500 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
              >
                <X size={16} />
                <span>Cancel</span>
              </button>
            </div>
          )}
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-900/30 border border-red-500/30 rounded-md">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-900/30 border border-green-500/30 rounded-md">
            <p className="text-green-400 text-sm">{success}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Display Name */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Display Name *
            </label>
            {isEditing ? (
              <input
                type="text"
                value={editData.displayName}
                onChange={(e) => setEditData({ ...editData, displayName: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                placeholder="Enter your display name"
                required
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <User size={16} className="text-gray-400" />
                <span className="text-white">{formData.displayName || "Not set"}</span>
              </div>
            )}
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Email Address *
            </label>
            {isEditing ? (
              <input
                type="email"
                value={editData.email}
                onChange={(e) => setEditData({ ...editData, email: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                placeholder="Enter your email address"
                required
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <Mail size={16} className="text-gray-400" />
                <span className="text-white">{formData.email || "Not set"}</span>
              </div>
            )}
          </div>

          {/* First Name */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              First Name
            </label>
            {isEditing ? (
              <input
                type="text"
                value={editData.firstName}
                onChange={(e) => setEditData({ ...editData, firstName: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                placeholder="Enter your first name"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <User size={16} className="text-gray-400" />
                <span className="text-white">{formData.firstName || "Not set"}</span>
              </div>
            )}
          </div>

          {/* Last Name */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Last Name
            </label>
            {isEditing ? (
              <input
                type="text"
                value={editData.lastName}
                onChange={(e) => setEditData({ ...editData, lastName: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                placeholder="Enter your last name"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <User size={16} className="text-gray-400" />
                <span className="text-white">{formData.lastName || "Not set"}</span>
              </div>
            )}
          </div>

          {/* Phone */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number
            </label>
            {isEditing ? (
              <input
                type="tel"
                value={editData.phone}
                onChange={(e) => setEditData({ ...editData, phone: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                placeholder="Enter your phone number"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <span className="text-gray-400">📞</span>
                <span className="text-white">{formData.phone || "Not set"}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Account Status */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <h3 className="text-xl font-bold text-yellow-400 mb-4">Account Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-green-400">Active</div>
            <div className="text-sm text-gray-400">Account Status</div>
          </div>
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-blue-400">
              {userData?.isEmailVerified ? "Verified" : "Pending"}
            </div>
            <div className="text-sm text-gray-400">Email Status</div>
          </div>
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-purple-400">
              {userData?.role || "Customer"}
            </div>
            <div className="text-sm text-gray-400">Account Type</div>
          </div>
        </div>

        {/* Database Connection Info */}
        <div className="mt-4 p-3 bg-gray-700 rounded-lg">
          <div className="text-sm text-gray-300">
            <strong>Database Connection:</strong> {userData ? "✅ Connected" : "❌ Not Connected"}
          </div>
          <div className="text-sm text-gray-300">
            <strong>User ID:</strong> {user?.id || "Not available"}
          </div>
          <div className="text-sm text-gray-300">
            <strong>Last Updated:</strong> {userData?.updatedAt ? new Date(userData.updatedAt).toLocaleString() : "Never"}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <h3 className="text-xl font-bold text-yellow-400 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-left">
            <div className="text-white font-medium">View Order History</div>
            <div className="text-gray-400 text-sm">Check your recent purchases</div>
          </button>
          <button className="p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-left">
            <div className="text-white font-medium">Account Settings</div>
            <div className="text-gray-400 text-sm">Manage your preferences</div>
          </button>
        </div>
      </div>
    </div>
  )
}