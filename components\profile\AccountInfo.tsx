"use client"

import { useState, useEffect } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { useAuth } from "@/contexts/AuthContext"
import { Edit, Save, X, Mail, Phone, MapPin, Calendar, Trophy, Star } from "lucide-react"

export default function AccountInfo() {
  const { t } = useLanguage()
  const { userData, updateUserData } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    country: "",
    city: "",
    dateOfBirth: "",
    bio: "",
  })

  const [editData, setEditData] = useState(formData)

  // Update form data when user data changes
  useEffect(() => {
    if (userData) {
      const newData = {
        firstName: userData.firstName || "",
        lastName: userData.lastName || "",
        email: userData.email || "",
        phone: userData.phone || "",
        country: userData.country || "",
        city: userData.city || "",
        dateOfBirth: userData.dateOfBirth || "",
        bio: userData.bio || "",
      }
      setFormData(newData)
      setEditData(newData)
    }
  }, [userData])

  const handleSave = async () => {
    try {
      await updateUserData({
        firstName: editData.firstName,
        lastName: editData.lastName,
        phone: editData.phone,
        country: editData.country,
        city: editData.city,
        dateOfBirth: editData.dateOfBirth,
        bio: editData.bio,
      })
      setFormData(editData)
      setIsEditing(false)
    } catch (error) {
      console.error("Error updating user data:", error)
      // You could add toast notification here
    }
  }

  const handleCancel = () => {
    setEditData(formData)
    setIsEditing(false)
  }

  const stats = {
    totalOrders: 15,
    totalSpent: 2450,
    memberSince: userData?.createdAt ? new Date(userData.createdAt).getFullYear() : new Date().getFullYear(),
    favoriteCategory: "Accounts",
  }

  return (
    <div className="space-y-6">
      {/* Account Statistics */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <h3 className="text-xl font-bold text-yellow-400 mb-4">Account Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{stats.totalOrders}</div>
            <div className="text-sm text-gray-400">Total Orders</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{stats.totalSpent} EGP</div>
            <div className="text-sm text-gray-400">Total Spent</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">{stats.memberSince}</div>
            <div className="text-sm text-gray-400">Member Since</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">
              <Trophy size={24} className="mx-auto" />
            </div>
            <div className="text-sm text-gray-400">Premium Member</div>
          </div>
        </div>
      </div>

      {/* Personal Information */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-yellow-400">Personal Information</h3>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              <Edit size={16} />
              <span>Edit</span>
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
              >
                <Save size={16} />
                <span>Save</span>
              </button>
              <button
                onClick={handleCancel}
                className="flex items-center space-x-2 bg-gray-500 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                <X size={16} />
                <span>Cancel</span>
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">First Name</label>
            {isEditing ? (
              <input
                type="text"
                value={editData.firstName}
                onChange={(e) => setEditData({ ...editData, firstName: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <span className="text-white">{formData.firstName || "Not set"}</span>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Last Name</label>
            {isEditing ? (
              <input
                type="text"
                value={editData.lastName}
                onChange={(e) => setEditData({ ...editData, lastName: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <span className="text-white">{formData.lastName || "Not set"}</span>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Email</label>
            <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
              <Mail size={16} className="text-gray-400" />
              <span className="text-white">{formData.email}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">Email cannot be changed. Contact support if needed.</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Phone</label>
            {isEditing ? (
              <input
                type="tel"
                value={editData.phone}
                onChange={(e) => setEditData({ ...editData, phone: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <Phone size={16} className="text-gray-400" />
                <span className="text-white">{formData.phone || "Not set"}</span>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Country</label>
            {isEditing ? (
              <select
                value={editData.country}
                onChange={(e) => setEditData({ ...editData, country: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
              >
                <option value="">Select a country</option>
                <option value="Egypt">Egypt</option>
                <option value="Saudi Arabia">Saudi Arabia</option>
                <option value="UAE">UAE</option>
                <option value="Jordan">Jordan</option>
                <option value="Lebanon">Lebanon</option>
                <option value="Other">Other</option>
              </select>
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <MapPin size={16} className="text-gray-400" />
                <span className="text-white">{formData.country || "Not set"}</span>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">City</label>
            {isEditing ? (
              <input
                type="text"
                value={editData.city}
                onChange={(e) => setEditData({ ...editData, city: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <MapPin size={16} className="text-gray-400" />
                <span className="text-white">{formData.city || "Not set"}</span>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Date of Birth</label>
            {isEditing ? (
              <input
                type="date"
                value={editData.dateOfBirth}
                onChange={(e) => setEditData({ ...editData, dateOfBirth: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
                <Calendar size={16} className="text-gray-400" />
                <span className="text-white">
                  {formData.dateOfBirth ? new Date(formData.dateOfBirth).toLocaleDateString() : "Not set"}
                </span>
              </div>
            )}
          </div>

          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">Bio</label>
            {isEditing ? (
              <textarea
                value={editData.bio}
                onChange={(e) => setEditData({ ...editData, bio: e.target.value })}
                rows={3}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:border-yellow-500 focus:outline-none"
                placeholder="Tell us about yourself..."
              />
            ) : (
              <div className="p-3 bg-gray-700 rounded-md">
                <span className="text-white">{formData.bio || "No bio provided"}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Gaming Preferences */}
      <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
        <h3 className="text-xl font-bold text-yellow-400 mb-4">Gaming Preferences</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Favorite Game Mode</label>
            <div className="p-3 bg-gray-700 rounded-md">
              <span className="text-white">Classic (Squad)</span>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Server</label>
            <div className="p-3 bg-gray-700 rounded-md">
              <span className="text-white">Middle East</span>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Favorite Category</label>
            <div className="p-3 bg-gray-700 rounded-md">
              <span className="text-white">{stats.favoriteCategory}</span>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Skill Level</label>
            <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-md">
              <div className="flex space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} size={16} className="text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-white">Expert</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
