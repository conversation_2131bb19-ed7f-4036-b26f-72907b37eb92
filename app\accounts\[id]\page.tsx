import type { Metadata } from "next"
import { notFound } from "next/navigation"
import ImageGallery from "@/components/ImageGallery"
import ProductSpecs from "@/components/ProductSpecs"
import AddToCartButton from "@/components/AddToCartButton"
import { Crown, Shield, Star, Trophy, Zap } from "lucide-react"

// Mock data for accounts (all demo items so /accounts/1-6 resolve)
const accountsData = {
  "1": {
    id: "1",
    name: { en: "Conqueror Account #1", ar: "حساب كونكرر #1" },
    description: {
      en: "Premium PUBG account with Conqueror rank and exclusive skins. This account features rare items, high-tier achievements, and a solid gaming history. Perfect for competitive players who want to start with an advantage.",
      ar: "حساب PUBG مميز برتبة كونكرر وسكينز حصرية. يحتوي هذا الحساب على عناصر نادرة وإنجازات عالية المستوى وتاريخ لعب قوي. مثالي للاعبين التنافسيين الذين يريدون البدء بميزة.",
    },
    price: { egp: 1500, usd: 50 },
    images: [
      "/placeholder.svg?height=400&width=600",
      "/placeholder.svg?height=400&width=600",
      "/placeholder.svg?height=400&width=600",
      "/placeholder.svg?height=400&width=600",
    ],
    rank: { current: "Conqueror", highest: "Conqueror", tier: "S1" },
    level: 85,
    stats: {
      kd: 3.2,
      winRate: 25,
      matches: 1250,
      wins: 312,
      kills: 4800,
      damage: 1250000,
    },
    specs: {
      rank: { en: "Conqueror", ar: "كونكرر" },
      level: { en: "Level 85", ar: "المستوى 85" },
      kd: { en: "3.2 K/D Ratio", ar: "3.2 نسبة القتل" },
      winRate: { en: "25% Win Rate", ar: "25% معدل الفوز" },
      totalMatches: { en: "1,250 Matches", ar: "1,250 مباراة" },
      totalKills: { en: "4,800 Kills", ar: "4,800 قتلة" },
      rp: { en: "Season 1-20 RP", ar: "رويال باس الموسم 1-20" },
      skins: { en: "50+ Legendary Skins", ar: "50+ سكن أسطوري" },
    },
    features: {
      en: [
        "Conqueror rank in multiple seasons",
        "50+ Legendary and Mythic skins",
        "Complete vehicle collection",
        "High-tier achievements and titles",
        "Clean account history - no bans",
        "Email and password changeable",
        "All Royal Pass rewards unlocked",
        "Premium crate items included",
      ],
      ar: [
        "رتبة كونكرر في مواسم متعددة",
        "50+ سكن أسطوري وخرافي",
        "مجموعة مركبات كاملة",
        "إنجازات وألقاب عالية المستوى",
        "تاريخ حساب نظيف - بدون حظر",
        "يمكن تغيير الإيميل وكلمة المرور",
        "جميع مكافآت الرويال باس مفتوحة",
        "عناصر الصناديق المميزة مشمولة",
      ],
    },
    skins: {
      outfits: [
        { name: "Pharaoh X-Suit", rarity: "Mythic", season: "M1" },
        { name: "Joker Set", rarity: "Legendary", season: "S8" },
        { name: "Glacier M416", rarity: "Legendary", season: "S2" },
        { name: "Dragon Hunter Set", rarity: "Legendary", season: "S12" },
      ],
      weapons: [
        { name: "Glacier M416", rarity: "Legendary" },
        { name: "Fool M416", rarity: "Legendary" },
        { name: "Golden M249", rarity: "Legendary" },
        { name: "Pharaoh AKM", rarity: "Mythic" },
      ],
      vehicles: [
        { name: "Golden Mirado", rarity: "Legendary" },
        { name: "Shark Bike", rarity: "Epic" },
        { name: "Pharaoh UAZ", rarity: "Mythic" },
      ],
    },
    achievements: [
      { name: "Chicken Master", description: "Win 100 matches", completed: true },
      { name: "Sharpshooter", description: "Get 1000 headshots", completed: true },
      { name: "Survivor", description: "Survive 500 matches", completed: true },
      { name: "Conqueror", description: "Reach Conqueror rank", completed: true },
    ],
  },

  "2": {
    id: "2",
    name: { en: "Crown Account #1", ar: "حساب كراون #1" },
    description: {
      en: "Premium account with Crown rank and multiple legendary skins. Great for competitive play with solid stats and rare items.",
      ar: "حساب مميز برتبة كراون وعدة سكنز أسطورية. رائع للعب التنافسي مع إحصائيات قوية وعناصر نادرة.",
    },
    price: { egp: 900, usd: 30 },
    images: ["/placeholder.svg?height=400&width=600", "/placeholder.svg?height=400&width=600"],
    rank: { current: "Crown", highest: "Crown", tier: "III" },
    level: 72,
    stats: {
      kd: 2.8,
      winRate: 22,
      matches: 980,
      wins: 216,
      kills: 3200,
      damage: 890000,
    },
    specs: {
      rank: { en: "Crown III", ar: "كراون III" },
      level: { en: "Level 72", ar: "المستوى 72" },
      kd: { en: "2.8 K/D Ratio", ar: "2.8 نسبة القتل" },
      winRate: { en: "22% Win Rate", ar: "22% معدل الفوز" },
      totalMatches: { en: "980 Matches", ar: "980 مباراة" },
      totalKills: { en: "3,200 Kills", ar: "3,200 قتلة" },
      skins: { en: "30+ Legendary Skins", ar: "30+ سكن أسطوري" },
    },
    features: {
      en: [
        "Crown rank achieved",
        "30+ Legendary skins",
        "Multiple season rewards",
        "Clean gaming history",
        "Changeable credentials",
        "Premium items included",
      ],
      ar: [
        "تم تحقيق رتبة كراون",
        "30+ سكن أسطوري",
        "مكافآت مواسم متعددة",
        "تاريخ لعب نظيف",
        "بيانات اعتماد قابلة للتغيير",
        "عناصر مميزة مشمولة",
      ],
    },
    skins: {
      outfits: [
        { name: "Joker Set", rarity: "Legendary", season: "S8" },
        { name: "School Shoes", rarity: "Legendary", season: "S1" },
      ],
      weapons: [
        { name: "Fool M416", rarity: "Legendary" },
        { name: "Golden AKM", rarity: "Epic" },
      ],
      vehicles: [{ name: "Shark Bike", rarity: "Epic" }],
    },
    achievements: [
      { name: "Crown Master", description: "Reach Crown rank", completed: true },
      { name: "Marksman", description: "Get 500 headshots", completed: true },
    ],
  },

  "3": {
    id: "3",
    name: { en: "Ace Account #1", ar: "حساب آيس #1" },
    description: {
      en: "Well-maintained account with Ace rank and good stats. Perfect for players looking for a solid foundation.",
      ar: "حساب محافظ عليه برتبة آيس وإحصائيات جيدة. مثالي للاعبين الذين يبحثون عن أساس قوي.",
    },
    price: { egp: 600, usd: 20 },
    images: ["/placeholder.svg?height=400&width=600"],
    rank: { current: "Ace", highest: "Ace", tier: "V" },
    level: 65,
    stats: {
      kd: 2.1,
      winRate: 18,
      matches: 750,
      wins: 135,
      kills: 2100,
      damage: 650000,
    },
    specs: {
      rank: { en: "Ace V", ar: "آيس V" },
      level: { en: "Level 65", ar: "المستوى 65" },
      kd: { en: "2.1 K/D Ratio", ar: "2.1 نسبة القتل" },
      winRate: { en: "18% Win Rate", ar: "18% معدل الفوز" },
      skins: { en: "15+ Epic Skins", ar: "15+ سكن ملحمي" },
    },
    features: {
      en: ["Ace rank achieved", "15+ Epic skins", "Good K/D ratio", "Clean account", "Changeable details"],
      ar: ["تم تحقيق رتبة آيس", "15+ سكن ملحمي", "نسبة قتل جيدة", "حساب نظيف", "تفاصيل قابلة للتغيير"],
    },
    skins: {
      outfits: [{ name: "School Set", rarity: "Epic", season: "S1" }],
      weapons: [{ name: "Silver AKM", rarity: "Epic" }],
      vehicles: [],
    },
    achievements: [{ name: "Ace Master", description: "Reach Ace rank", completed: true }],
  },

  "4": {
    id: "4",
    name: { en: "Diamond Account #1", ar: "حساب دايموند #1" },
    description: {
      en: "Solid account for competitive play with Diamond rank and decent collection.",
      ar: "حساب قوي للعب التنافسي برتبة دايموند ومجموعة جيدة.",
    },
    price: { egp: 450, usd: 15 },
    images: ["/placeholder.svg?height=400&width=600"],
    rank: { current: "Diamond", highest: "Diamond", tier: "II" },
    level: 58,
    stats: {
      kd: 1.8,
      winRate: 15,
      matches: 600,
      wins: 90,
      kills: 1500,
      damage: 450000,
    },
    specs: {
      rank: { en: "Diamond II", ar: "دايموند II" },
      level: { en: "Level 58", ar: "المستوى 58" },
      kd: { en: "1.8 K/D Ratio", ar: "1.8 نسبة القتل" },
      skins: { en: "10+ Rare Skins", ar: "10+ سكن نادر" },
    },
    features: {
      en: ["Diamond rank", "10+ Rare skins", "Decent stats", "Clean history"],
      ar: ["رتبة دايموند", "10+ سكن نادر", "إحصائيات جيدة", "تاريخ نظيف"],
    },
    skins: {
      outfits: [{ name: "Combat Set", rarity: "Rare", season: "S3" }],
      weapons: [{ name: "Blue AKM", rarity: "Rare" }],
      vehicles: [],
    },
    achievements: [{ name: "Diamond Master", description: "Reach Diamond rank", completed: true }],
  },

  "5": {
    id: "5",
    name: { en: "Platinum Account #1", ar: "حساب بلاتينيوم #1" },
    description: {
      en: "Great starter account with Platinum rank and decent items for new competitive players.",
      ar: "حساب بداية رائع برتبة بلاتينيوم وعناصر جيدة للاعبين التنافسيين الجدد.",
    },
    price: { egp: 300, usd: 10 },
    images: ["/placeholder.svg?height=400&width=600"],
    rank: { current: "Platinum", highest: "Platinum", tier: "I" },
    level: 45,
    stats: {
      kd: 1.5,
      winRate: 12,
      matches: 400,
      wins: 48,
      kills: 800,
      damage: 280000,
    },
    specs: {
      rank: { en: "Platinum I", ar: "بلاتينيوم I" },
      level: { en: "Level 45", ar: "المستوى 45" },
      kd: { en: "1.5 K/D Ratio", ar: "1.5 نسبة القتل" },
      skins: { en: "5+ Common Skins", ar: "5+ سكن عادي" },
    },
    features: {
      en: ["Platinum rank", "5+ Common skins", "Good for beginners"],
      ar: ["رتبة بلاتينيوم", "5+ سكن عادي", "جيد للمبتدئين"],
    },
    skins: {
      outfits: [],
      weapons: [],
      vehicles: [],
    },
    achievements: [{ name: "Platinum Master", description: "Reach Platinum rank", completed: true }],
  },

  "6": {
    id: "6",
    name: { en: "Gold Account #1", ar: "حساب ذهبي #1" },
    description: {
      en: "Budget-friendly account for new players with Gold rank and basic items.",
      ar: "حساب اقتصادي للاعبين الجدد برتبة ذهبية وعناصر أساسية.",
    },
    price: { egp: 150, usd: 5 },
    images: ["/placeholder.svg?height=400&width=600"],
    rank: { current: "Gold", highest: "Gold", tier: "III" },
    level: 32,
    stats: {
      kd: 1.2,
      winRate: 8,
      matches: 250,
      wins: 20,
      kills: 400,
      damage: 150000,
    },
    specs: {
      rank: { en: "Gold III", ar: "ذهبي III" },
      level: { en: "Level 32", ar: "المستوى 32" },
      kd: { en: "1.2 K/D Ratio", ar: "1.2 نسبة القتل" },
      skins: { en: "Basic Items", ar: "عناصر أساسية" },
    },
    features: {
      en: ["Gold rank", "Basic items", "Perfect for starters"],
      ar: ["رتبة ذهبية", "عناصر أساسية", "مثالي للمبتدئين"],
    },
    skins: {
      outfits: [],
      weapons: [],
      vehicles: [],
    },
    achievements: [{ name: "Gold Master", description: "Reach Gold rank", completed: true }],
  },
} satisfies Record<string, any>

interface PageProps {
  params: { id: string }
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = params
  const account = accountsData[id as keyof typeof accountsData]

  if (!account) {
    return {
      title: "Account Not Found - RNG VIP",
    }
  }

  return {
    title: `${account.name.en} - RNG VIP`,
    description: account.description.en,
  }
}

export default async function AccountDetailPage({ params }: PageProps) {
  const { id } = params
  const account = accountsData[id as keyof typeof accountsData]

  if (!account) {
    notFound()
  }

  const getRankIcon = (rank: string) => {
    switch (rank.toLowerCase()) {
      case "conqueror":
        return <Crown className="text-yellow-400" size={24} />
      case "crown":
        return <Crown className="text-purple-400" size={24} />
      case "ace":
        return <Star className="text-blue-400" size={24} />
      case "diamond":
        return <Zap className="text-cyan-400" size={24} />
      default:
        return <Trophy className="text-gray-400" size={24} />
    }
  }

  const getRankColor = (rank: string) => {
    switch (rank.toLowerCase()) {
      case "conqueror":
        return "text-yellow-400 bg-yellow-400/10 border-yellow-400/30"
      case "crown":
        return "text-purple-400 bg-purple-400/10 border-purple-400/30"
      case "ace":
        return "text-blue-400 bg-blue-400/10 border-blue-400/30"
      case "diamond":
        return "text-cyan-400 bg-cyan-400/10 border-cyan-400/30"
      case "platinum":
        return "text-gray-300 bg-gray-300/10 border-gray-300/30"
      default:
        return "text-yellow-600 bg-yellow-600/10 border-yellow-600/30"
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Gallery */}
        <div>
          <ImageGallery images={account.images} productName={account.name.en} />
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <div className="flex items-center space-x-4 mb-4">
              <h1 className="text-3xl font-bold text-yellow-400">{account.name.en}</h1>
              <div
                className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${getRankColor(account.rank.current)}`}
              >
                {getRankIcon(account.rank.current)}
                <span className="font-bold">
                  {account.rank.current} {account.rank.tier}
                </span>
              </div>
            </div>
            <h2 className="text-2xl font-semibold text-gray-300 mb-4">{account.name.ar}</h2>
            <p className="text-gray-300 leading-relaxed">{account.description.en}</p>
            <p className="text-gray-400 leading-relaxed mt-2">{account.description.ar}</p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-800 rounded-lg p-4 text-center border border-yellow-500/20">
              <div className="text-2xl font-bold text-yellow-400">{account.stats.kd}</div>
              <div className="text-sm text-gray-400">K/D Ratio</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center border border-green-500/20">
              <div className="text-2xl font-bold text-green-400">{account.stats.winRate}%</div>
              <div className="text-sm text-gray-400">Win Rate</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center border border-blue-500/20">
              <div className="text-2xl font-bold text-blue-400">{account.level}</div>
              <div className="text-sm text-gray-400">Level</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center border border-purple-500/20">
              <div className="text-2xl font-bold text-purple-400">{account.stats.matches}</div>
              <div className="text-sm text-gray-400">Matches</div>
            </div>
          </div>

          {/* Security Badge */}
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <Shield className="text-green-400" size={24} />
              <div>
                <h3 className="text-green-400 font-semibold">Verified Account</h3>
                <p className="text-gray-300 text-sm">
                  Clean history, no bans, email and password changeable after purchase.
                </p>
              </div>
            </div>
          </div>

          {/* Add to Cart */}
          <AddToCartButton productId={account.id} productType="account" price={account.price} />
        </div>
      </div>

      {/* Detailed Stats */}
      <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ProductSpecs specs={account.specs} />

        <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
          <h3 className="text-xl font-bold text-yellow-400 mb-4">Detailed Statistics</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b border-gray-700">
              <span className="text-gray-300">Total Kills</span>
              <span className="text-white font-semibold">{account.stats.kills.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-700">
              <span className="text-gray-300">Total Wins</span>
              <span className="text-white font-semibold">{account.stats.wins}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-700">
              <span className="text-gray-300">Total Damage</span>
              <span className="text-white font-semibold">{account.stats.damage.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-300">Highest Rank</span>
              <span className="text-white font-semibold">{account.rank.highest}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="mt-8">
        <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
          <h3 className="text-xl font-bold text-yellow-400 mb-4">Account Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-white mb-3">English</h4>
              <ul className="space-y-2">
                {account.features.en.map((feature, index) => (
                  <li key={index} className="text-gray-300 text-sm flex items-start">
                    <span className="text-yellow-400 mr-2">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-3">العربية</h4>
              <ul className="space-y-2">
                {account.features.ar.map((feature, index) => (
                  <li key={index} className="text-gray-300 text-sm flex items-start">
                    <span className="text-yellow-400 mr-2">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Skins & Items */}
      {account.skins && (
        <div className="mt-8">
          <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
            <h3 className="text-xl font-bold text-yellow-400 mb-4">Included Skins & Items</h3>

            {account.skins.outfits.length > 0 && (
              <div className="mb-6">
                <h4 className="font-semibold text-white mb-3">Outfits</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {account.skins.outfits.map((outfit, index) => (
                    <div key={index} className="bg-gray-700 rounded-lg p-3 flex justify-between items-center">
                      <div>
                        <span className="text-white font-medium">{outfit.name}</span>
                        <span className="text-gray-400 text-sm ml-2">({outfit.season})</span>
                      </div>
                      <span
                        className={`px-2 py-1 rounded-md text-xs font-medium ${
                          outfit.rarity === "Mythic"
                            ? "bg-red-500/20 text-red-400"
                            : outfit.rarity === "Legendary"
                              ? "bg-yellow-500/20 text-yellow-400"
                              : "bg-purple-500/20 text-purple-400"
                        }`}
                      >
                        {outfit.rarity}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {account.skins.weapons.length > 0 && (
              <div className="mb-6">
                <h4 className="font-semibold text-white mb-3">Weapon Skins</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {account.skins.weapons.map((weapon, index) => (
                    <div key={index} className="bg-gray-700 rounded-lg p-3 flex justify-between items-center">
                      <span className="text-white font-medium">{weapon.name}</span>
                      <span
                        className={`px-2 py-1 rounded-md text-xs font-medium ${
                          weapon.rarity === "Mythic"
                            ? "bg-red-500/20 text-red-400"
                            : weapon.rarity === "Legendary"
                              ? "bg-yellow-500/20 text-yellow-400"
                              : "bg-purple-500/20 text-purple-400"
                        }`}
                      >
                        {weapon.rarity}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {account.skins.vehicles.length > 0 && (
              <div>
                <h4 className="font-semibold text-white mb-3">Vehicle Skins</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {account.skins.vehicles.map((vehicle, index) => (
                    <div key={index} className="bg-gray-700 rounded-lg p-3 flex justify-between items-center">
                      <span className="text-white font-medium">{vehicle.name}</span>
                      <span
                        className={`px-2 py-1 rounded-md text-xs font-medium ${
                          vehicle.rarity === "Mythic"
                            ? "bg-red-500/20 text-red-400"
                            : vehicle.rarity === "Legendary"
                              ? "bg-yellow-500/20 text-yellow-400"
                              : "bg-purple-500/20 text-purple-400"
                        }`}
                      >
                        {vehicle.rarity}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Achievements */}
      {account.achievements && account.achievements.length > 0 && (
        <div className="mt-8">
          <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
            <h3 className="text-xl font-bold text-yellow-400 mb-4">Achievements</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {account.achievements.map((achievement, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-4 flex items-center space-x-3">
                  <Trophy className="text-yellow-400" size={20} />
                  <div>
                    <h4 className="text-white font-medium">{achievement.name}</h4>
                    <p className="text-gray-400 text-sm">{achievement.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
